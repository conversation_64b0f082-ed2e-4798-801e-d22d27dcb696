<template>
  <div v-if="pageConfig" class="user-login-main" :style="loginBackgroundStyle">
    <div class="login-box-wrap">
      <el-alert type="warning" class="login-alert" center :closeable="false">
        <div class="login-alert-item">护网期间仅支持验证码登录，给您带来的不便敬请谅解。</div>
        <div class="login-alert-item">
          温馨提示：运营商限制每个手机号每日验证码的短信发送数量，若点击发送验证码后系统提示"触发小时级/天级流控"，请耐心等待。具体限制要求：每小时内发送上限：10条；每个自然日内发送上限：20条。
        </div>
      </el-alert>
      <div class="content-box title-box">
        <div class="title-wrap">
          <div class="title">Welcome</div>
          <div class="title main-title">{{ pageTitle }}</div>
          <div class="title title-tip">专业 智慧 高效</div>
        </div>
      </div>
      <div class="content-box login-box">
        <div
          class="switch-bar"
          :class="{ disabled: !canAccountLogin || !HAS_DING_DING || configLoginType.length < 2 }"
        >
          <div
            v-if="HAS_DING_DING && configLoginType.includes(SC_LOGIN_TYPE.l2v('扫码登录'))"
            class="switch-btn"
            :class="{ 'is-active': loginType === SC_LOGIN_TYPE.l2v('扫码登录') }"
            @click="doSwitchType(SC_LOGIN_TYPE.l2v('扫码登录'))"
          >
            扫码登录
          </div>
          <div
            v-if="canAccountLogin && configLoginType.includes(SC_LOGIN_TYPE.l2v('密码登录'))"
            class="switch-btn"
            :class="{ 'is-active': loginType === SC_LOGIN_TYPE.l2v('密码登录') }"
            @click="doSwitchType(SC_LOGIN_TYPE.l2v('密码登录'))"
          >
            账号登录
          </div>
          <div
            v-if="configLoginType.includes(SC_LOGIN_TYPE.l2v('手机验证码登录'))"
            class="switch-btn"
            :class="{ 'is-active': loginType === SC_LOGIN_TYPE.l2v('手机验证码登录') }"
            @click="doSwitchType(SC_LOGIN_TYPE.l2v('手机验证码登录'))"
          >
            手机验证码登录
          </div>
        </div>
        <div v-show="loginType === SC_LOGIN_TYPE.l2v('扫码登录')" class="qrcode-wrap">
          <div id="qrlogin_container" class="dd-qrcode"></div>
          <!-- <div class="tools-bar">
            <el-checkbox v-model="isRember">长期登录</el-checkbox>
          </div> -->
        </div>
        <div v-if="loginType === SC_LOGIN_TYPE.l2v('密码登录')" class="account-wrap">
          <div class="ipt-wrap">
            <i class="icon-label fa fa-user ftp"></i>
            <input class="ipt" type="text" placeholder="请输入账户" v-model="auAccount" />
          </div>
          <div class="ipt-wrap">
            <i class="icon-label fa fa-lock ftp"></i>
            <input
              class="ipt"
              type="password"
              placeholder="请输入密码"
              v-model="auPassword"
              @keyup.enter="doLogin"
            />
          </div>
          <div class="tools-bar">
            <el-checkbox v-model="isRemberAccount">记住账号</el-checkbox>
          </div>
          <div class="login-btn" @click="doLogin">登 录</div>
        </div>
        <div
          v-if="loginType === SC_LOGIN_TYPE.l2v('手机验证码登录')"
          class="account-wrap mobile-wrap"
        >
          <div class="ipt-wrap">
            <i class="icon-label fa fa-user ftp"></i>
            <input class="ipt" type="text" placeholder="请输入账户" v-model="auAccount" />
          </div>
          <div class="ipt-wrap">
            <i class="icon-label fa fa-lock ftp"></i>
            <input
              class="ipt"
              placeholder="请输入验证码"
              v-model="verifyCode"
              @keyup.enter="doLogin"
            />
            <div class="get-verify-code-button" @click="getVerificationCode()">
              {{ verificationText }}
            </div>
          </div>
          <div class="tools-bar">
            <el-checkbox v-model="isRemberAccount">记住账号</el-checkbox>
          </div>
          <div class="login-btn" @click="doLogin">登 录</div>
        </div>
      </div>
    </div>
    <div class="bottom-info">
      <img style="width: 16px" src="@/assets/images/logo-icon.png" alt="数智造价" />
      <span>浙江云程信息科技有限公司</span>
      <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备**********号-1</a>
      <a
        target="_blank"
        href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************"
      >
        <img src="@/assets/images/icon/ic-beian.png" />
        浙公网安备 **************号
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
// import type { Route } from 'vue-router'
import { apiAutoGetDdKey } from '@/api/auto/request/baseConfig'
import { apiAutoLogin, apiAutoLoginBySms } from '@/api/auto/request/system'
import { projectBaseApiUrl } from '@/common/config'
import { getNumberArray, loginSetMemberToken } from '@/common/tools'
import { deleteMemberAccount, getMemberAccount, setMemberAccount } from '@/libs/utils/user'
import { AppConfigModule } from '@/store/modules/appConfig'
import { SC_DBSX_TIP_TYPE, SC_LOGIN_TYPE } from '@/views/systemConfig/constants'

import JSEncrypt from 'jsencrypt'
import { apiAutoSendSms } from '@/api/auto/request/verifyCode'

const HAS_DING_DING = process.env.VUE_APP_HAS_DINGDING === 'true'

type TPageConfig = Pick<apiAutoModel.BaseConfigModel, 'ddAppkeyLogin'> &
  Pick<
    apiAutoModel.SwitchConfigModel,
    | 'iconConfig'
    | 'titleConfig'
    | 'logoConfig'
    | 'isLoginType'
    | 'isLoginDuration'
    | 'defaultLoginMethod'
    | 'loginBgImg'
    | 'isToDoType'
    | 'isCheckDefaultPassword'
  >

@Component
export default class Login extends Vue {
  HAS_DING_DING = HAS_DING_DING
  SC_LOGIN_TYPE = SC_LOGIN_TYPE

  private auAccount = ''
  private auPassword = ''
  private verifyCode = ''

  canAccountLogin = true
  loginType = SC_LOGIN_TYPE.l2v('密码登录')
  isRemberAccount = false
  isRember = true

  pageConfig: TPageConfig | null = null
  encrypt: JSEncrypt = new JSEncrypt()

  /** 配置的可以登录的模式 */
  get configLoginType() {
    return getNumberArray(this.pageConfig?.isLoginType)
  }

  /** 配置的登录时长, 默认 1 天 */
  get configLoginDuration() {
    return this.pageConfig?.isLoginDuration ?? 1
  }

  /** 待办事项提醒类型 */
  get configDbsxTipType() {
    return this.pageConfig?.isToDoType ?? SC_DBSX_TIP_TYPE.l2v('每次登录提醒')
  }

  get pageTitle() {
    return this.pageConfig?.titleConfig || '数智造价'
  }

  get loginBackgroundStyle() {
    const loginBgImg =
      'https://fuhe-static-oss.oss-cn-nanjing.aliyuncs.com/web-resource/login_bg.jpg'
    if (loginBgImg) {
      return { 'background-image': `url(${loginBgImg})` }
    } else {
      return {}
    }
  }

  initEncrypt() {
    this.encrypt.setPublicKey(
      '-----BEGIN PUBLIC KEY-----' +
        process.env.VUE_APP_LOGIN_PUBLIC_KEY +
        '-----END PUBLIC KEY-----'
    )
  }

  created() {
    this.initEncrypt()
  }

  @Watch('isRember')
  onIsRemberInFifteenDaysChange() {
    if (HAS_DING_DING) {
      this.initDingDingLogin()
    }
  }

  // @Watch('$route', { immediate: true })
  // async onRouteChange(route: Route) {
  //   const query = route.query
  //   const isProduction = ['PRODUCTION'].includes(process.env.VUE_APP_ENV)
  //   this.canAccountLogin = query.admin === 'admin' || !isProduction || !HAS_DING_DING
  //   if (!this.canAccountLogin) {
  //     this.loginType = 'SCAN'
  //   } else if (!isProduction) {
  //     this.loginType = 'ACCOUNT'
  //     this.auPassword = process.env.VUE_APP_LOGIN_PASSWORD
  //   }
  // }

  async mounted() {
    await this.getPageConfig()
    if (HAS_DING_DING) {
      this.initDingDingLogin()
    }
    this.initRemberAccount()
  }

  /** 切换登录类型 */
  doSwitchType(loginType: number) {
    this.loginType = loginType
  }

  async getPageConfig() {
    const { data } = await apiAutoGetDdKey<TPageConfig>()
    this.pageConfig = data
    this.loginType = this.pageConfig.defaultLoginMethod ?? SC_LOGIN_TYPE.l2v('密码登录')
    AppConfigModule.applyPageConfig(data)
  }

  /** 初始化钉钉的登录 */
  async initDingDingLogin() {
    // 钉钉的APPID
    const APPID = this.pageConfig?.ddAppkeyLogin ?? ''
    // 回调地址
    const REDIRECT_URI = encodeURIComponent(
      `${projectBaseApiUrl}/System/AdminLoginInQR?loginDuration=${this.configLoginDuration}&showTodo=1`
    )
    window.DDLogin({
      id: 'qrlogin_container', // 页面容器 id
      goto: encodeURIComponent(
        `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${APPID}&response_type=code&scope=snsapi_login&redirect_uri=${REDIRECT_URI}`
      ),
      style: 'border:none;background-color:#f9fbfa;',
      width: '325',
      height: '310',
    })
    const hanndleMessage = function (event: MessageEvent) {
      const origin = event.origin
      if (origin === 'https://login.dingtalk.com') {
        // 判断是否来自 ddLogin 扫码事件。
        const loginTmpCode = event.data // 拿到 loginTmpCode 后就可以在这里构造跳转链接进行跳转了
        const url = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${APPID}&response_type=code&scope=snsapi_login&redirect_uri=${REDIRECT_URI}&loginTmpCode=${loginTmpCode}`
        location.href = url
      }
    }
    if (typeof window.addEventListener !== 'undefined') {
      window.addEventListener('message', hanndleMessage, false)
    } else if (typeof window.attachEvent !== 'undefined') {
      window.attachEvent('onmessage', hanndleMessage)
    }
  }

  /** 初始化记住账号逻辑 */
  initRemberAccount() {
    const saveMemberAccount = getMemberAccount()
    if (saveMemberAccount) {
      this.isRemberAccount = true
      this.auAccount = saveMemberAccount
    } else {
      this.isRemberAccount = false
      this.auAccount = ''
    }
  }

  /** 登录成功 跳转首页 */
  async loginSuccessJumpToHome() {
    const isShowTodo = this.configDbsxTipType !== SC_DBSX_TIP_TYPE.l2v('每日首次访问首页提醒')
    const params = [isShowTodo ? '?showTodo=1' : ''].join('&')
    this.gUrlJump(`/${params.length ? `?${params}` : ''}`)
  }

  /** 进行登录 */
  async doLogin() {
    if (!this.auAccount) {
      this.$message.error('请输入账户')
    }
    let loginData: apiCustomModel.ITokenModel | null = null
    if (this.loginType === SC_LOGIN_TYPE.l2v('密码登录')) {
      if (!this.auPassword) {
        this.$message.error('请输入密码')
      }
      const encryptedPassword = this.encrypt.encrypt(this.auPassword)
      // 返回false的情况，配置正确正常不会返回false
      if (!encryptedPassword) return
      const { data } = await apiAutoLogin<apiCustomModel.ITokenModel>({
        auAccount: this.auAccount,
        auPassword: encryptedPassword,
      })
      loginData = data
    } else if (this.loginType === SC_LOGIN_TYPE.l2v('手机验证码登录')) {
      if (!this.verifyCode) {
        this.$message.error('请输入验证码')
      }
      const { data } = await apiAutoLoginBySms<apiCustomModel.ITokenModel>({
        auAccount: this.auAccount,
        verifyCode: this.verifyCode,
      })
      loginData = data
    }
    if (this.isRemberAccount) {
      setMemberAccount(this.auAccount, 365)
    } else {
      deleteMemberAccount()
    }
    if (loginData) {
      loginSetMemberToken(loginData.token, this.configLoginDuration)
    }

    // 登录成功，跳转首页
    this.loginSuccessJumpToHome()
  }

  // #region 获取验证码相关逻辑
  verificationCode = ''
  verificationText = '获取验证码'
  verificationCount = 60
  verificationTimer: number | null = null

  /** 开始 验证码 timer */
  startVerificationTimer() {
    if (this.verificationTimer) return
    this.verificationTimer = setInterval(() => {
      this.verificationCount--
      if (this.verificationCount <= 0) {
        this.verificationText = '获取验证码'
        this.verificationCount = 60
        this.clearVerificationTimer()
      } else {
        this.verificationText = `（${this.verificationCount}）`
      }
    }, 1000)
  }

  /** 清除 验证码 timer */
  clearVerificationTimer() {
    if (this.verificationTimer) {
      clearInterval(this.verificationTimer)
      this.verificationTimer = null
    }
  }

  /** 获取验证码 */
  async getVerificationCode() {
    if (this.verificationTimer) return
    if (!this.auAccount) {
      this.$message.error('请先输入手机号')
      return
    }
    await apiAutoSendSms({ mobile: this.auAccount })
    this.startVerificationTimer()
  }
  // #endregion
}
</script>

<style lang="scss" scoped>
.login-alert {
  position: absolute;
  width: 1220px;
  margin: 0 auto;
  top: 0;

  transform: translateY(calc(-100% - 30px));

  .login-alert-item {
    text-align: center;
  }

  ::v-deep(.el-alert__description) {
    font-size: 16px;
  }
}
</style>
