<template>
  <div
    class="mc-archive-file-part scroll-cotar"
    :style="{ height: isOverviewPart ? 'auto' : contentHeight + 'px' }"
  >
    <!-- 归档信息 -->
    <ProjectArchiveForm
      v-if="projectInfo"
      :mustFileTypeData="mustFileTypeData"
      :isOverviewPart="isOverviewPart"
      :propProjectInfo="selectedProjectInfo"
      @archiveSuccess="handleArchiveSuccess"
      ref="projectArchiveForm"
    >
      <template v-slot="{ formData }">
        <slot :formData="formData"></slot>
      </template>
    </ProjectArchiveForm>
    <!-- 归档相关文件 -->
    <div class="el-table el-table--fit el-table--scrollable-x el-table--enable-row-transition">
      <table class="el-table__body" style="width: 100%">
        <colgroup>
          <col width="145" />
          <col width="auto" />
          <col width="125" v-if="!isReadonly" />
        </colgroup>
        <tr class="el-table__row" v-for="row in fileGroupData" :key="row.id">
          <td class="el-table__cell">
            <div class="cell txtr">
              <span v-if="row.dtIsWrite" class="must">*</span>
              {{ row.udName }}
            </div>
          </td>
          <td class="el-table__cell">
            <div class="cell">
              <ProjectFilelistUpload
                :historyQueryUrl="fileHistoryQueryUrl"
                :fileList="row._fileList"
                :prop="{
                  name: 'pfName',
                  url: 'pfRout',
                  size: 'pfSize',
                  user: 'createUser',
                  date: 'createTime',
                  groupKey: 'pfGroup',
                  isHistory: 'pfIsHis',
                }"
                :disabled="isReadonly"
                :isReCheckReplace="!!row.dtIsFhReplace"
                :deleteBtn="!!row.dtIsDelete"
                @deleteFile="handleDeleteFile"
                @replaceFile="item => doReplaceFile(row.id, item.pfGroup)"
              />
            </div>
          </td>
          <td v-if="!isReadonly" class="el-table__cell">
            <div class="cell">
              <el-button
                v-if="!row.dtIsMultiple && row._fileList.length > 0"
                icon="el-icon-upload2"
                @click="doReplaceFile(row.id, row._fileList[0].pfGroup)"
              >
                替换文件
              </el-button>
              <el-button
                v-else
                icon="el-icon-upload2"
                @click="doUploadFile(row.id, !!row.dtIsMultiple)"
              >
                上传文件
              </el-button>
            </div>
          </td>
        </tr>
      </table>
    </div>
    <slot name="formBottom"></slot>
    <!-- TODO: 归档文件 -->
    <ArchiveGcFilePart />
    <!-- 项目增印报告 -->
    <ProjectOpinionOverPrint />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref } from 'vue-property-decorator'
import { apiAutoGetProjectFileGdList } from '@/api/auto/request/projectFileGd'
import { AppModule } from '@/store/modules/app'
import { ProjectModule } from '@/store/modules/project'
import ProjectArchiveForm from './components/ProjectArchiveForm.vue'
import { apiAutoGetDocumentTemplateList } from '@/api/auto/request/documentTemplate'
import ProjectFilelistUpload from '@/components/project/ProjectFilelistUpload/index.vue'
import {
  type ICommonProjectFilelistUploadConfig,
  ProjectFileGdUploadMixin,
  type TRefreshData,
} from '@/components/project/ProjectFilelistUpload/commonProjectFilelistUpload'
import ProjectOpinionOverPrint from './components/ProjectOpinionOverPrint/index.vue'
import { DOC_TEMPLATE_TAB } from '@/views/systemConfig/constants'
import { PROJECT_INFO_STATE_V2 } from '@/views/project/constants'
import ArchiveGcFilePart from '../InfoPanel/components/ArchiveGcFilePart/index.vue'

@Component({
  components: {
    ProjectArchiveForm,
    ProjectFilelistUpload,
    ProjectOpinionOverPrint,
    ArchiveGcFilePart,
  },
})
export default class ArchiveFilePart
  extends ProjectFileGdUploadMixin
  implements ICommonProjectFilelistUploadConfig
{
  @Prop({ type: Boolean, default: false }) readonly readonly!: boolean
  @Prop({ type: Number, default: 0 }) readonly fixedHeightAdapter!: number
  /** 是否在进度总览中当 part */
  @Prop({ type: Boolean, default: false }) readonly isOverviewPart!: boolean
  /** 是否在项目归档当 part */
  @Prop({ type: Boolean, default: false }) readonly isPopPart!: boolean

  @Ref() readonly projectArchiveForm!: ProjectArchiveForm

  get isInElectronApp() {
    return AppModule.isInElectronApp
  }

  get contentHeight() {
    return AppModule.customPageHeight - this.fixedHeightAdapter
  }

  selectedProjectInfo: apiAutoModel.ReqVProjectInfoModel | null = null
  fileGroupData: apiExtendsModel.VDocumentTemplateModel[] = []

  get commonProjectUploadReSubmitData() {
    return { pfPiId: this.projectInfo.id ?? 0 }
  }

  get projectInfo() {
    return (ProjectModule.projectInfo ||
      this.selectedProjectInfo) as apiAutoModel.ReqVProjectInfoModel
  }

  get projectLoginUserInfo() {
    return ProjectModule.projectLoginUserInfoWithStatus
  }

  get isProManager() {
    return !!this.projectLoginUserInfo._isPM
  }

  get isReadonly() {
    if (
      this.projectLoginUserInfo._isM &&
      !this.projectLoginUserInfo.puIsWp &&
      this.projectInfo.piState &&
      this.projectInfo.piState <= PROJECT_INFO_STATE_V2.l2v('待提交归档')
    )
      return false

    return this.isOverviewPart || this.readonly
  }

  /** 必填文件类型数据 */
  get mustFileTypeData() {
    return this.fileGroupData.filter(v => v.dtIsWrite)
  }

  created() {
    if (!this.isPopPart) {
      this.initData()
    }
    if (this.isInElectronApp && window.MCElectronBridge) {
      window.MCElectronBridge.off('gpy-exit')
      window.MCElectronBridge.on('gpy-exit', () => {
        this.refreshData('upload')
      })
    }
  }

  destroyed() {
    if (this.isInElectronApp && window.MCElectronBridge) {
      window.MCElectronBridge.off('gpy-exit')
    }
  }

  async initData(projectInfo: apiAutoModel.ReqVProjectInfoModel | null = null, isRefresh = false) {
    if (projectInfo) {
      this.selectedProjectInfo = projectInfo
    }
    if (!isRefresh) {
      this.$nextTick(function () {
        this.projectArchiveForm.setValue(this.projectInfo)
      })
    }
    const [fileTypeRes, fileRes] = await Promise.all([
      apiAutoGetDocumentTemplateList<apiAutoModel.VDocumentTemplateModel[]>({
        btId: this.projectInfo?.piSecBtid,
        dtType: DOC_TEMPLATE_TAB.l2v('项目归档文档模板'),
        queryAll: 1,
      }),
      apiAutoGetProjectFileGdList<apiAutoModel.ProjectFileGdModel[]>({
        pfPiId: this.projectInfo.id,
        pfIsHis: 0,
        queryAll: 1,
      }),
    ])
    const fileData = fileRes.data
    const fileGroupData = fileTypeRes.data as apiExtendsModel.VDocumentTemplateModel[]
    fileGroupData.forEach(v => {
      v._fileList = fileData.filter(n => n.pfTypeId === v.id)
    })
    this.fileGroupData = fileGroupData
  }

  refreshData(type: TRefreshData) {
    this.initData(null, true)
    if (!this.isPopPart && type === 'upload') {
      // 刷新外层组件用户状态
      ProjectModule.refreshProjectLoginUserInfoForce()
    }
  }

  /** 归档成功 */
  handleArchiveSuccess() {
    this.$emit('archiveSuccess')
  }
}
</script>
