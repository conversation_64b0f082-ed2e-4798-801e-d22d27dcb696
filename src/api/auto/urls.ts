/**
 * 根据 swagger api 自动生成, 请勿修改
 * autogeneration by /build/swagger/index.js
 */

export const apiAutoAcceptDataConfig = {
  /** 新增接受资料配置 */
  AddAcceptDataConfig: '/AcceptDataConfig/AddAcceptDataConfig',
  /** 删除接受资料配置 */
  DeleteAcceptDataConfig: '/AcceptDataConfig/DeleteAcceptDataConfig',
  /** 获取接受资料配置列表 */
  GetAcceptDataConfigList: '/AcceptDataConfig/GetAcceptDataConfigList',
  /** 修改接受资料配置 */
  UpdateAcceptDataConfig: '/AcceptDataConfig/UpdateAcceptDataConfig',
  /** 修改排序 */
  UpdateAcceptDataConfigSort: '/AcceptDataConfig/UpdateAcceptDataConfigSort',
}
export const apiAutoAcceptDataType = {
  /** 新增资料类型 */
  AddAcceptDataType: '/AcceptDataType/AddAcceptDataType',
  /** 删除资料类型 */
  DeleteAcceptDataType: '/AcceptDataType/DeleteAcceptDataType',
  /** 获取资料类型列表 */
  GetAcceptDataTypeList: '/AcceptDataType/GetAcceptDataTypeList',
  /** 修改资料类型 */
  UpdateAcceptDataType: '/AcceptDataType/UpdateAcceptDataType',
  /** 修改排序 */
  UpdateAcceptDataTypeSort: '/AcceptDataType/UpdateAcceptDataTypeSort',
}
export const apiAutoAcceptDataZj = {
  /** 新增造价咨询接收资料管理 */
  AddAcceptDataZj: '/AcceptDataZj/AddAcceptDataZj',
  /** 批量新增造价咨询接收资料管理 */
  BathAddAcceptDataZj: '/AcceptDataZj/BathAddAcceptDataZj',
  /** 批量修改造价咨询接收资料管理 */
  BathUpdateAcceptDataZj: '/AcceptDataZj/BathUpdateAcceptDataZj',
  /** 删除造价咨询接收资料管理 */
  DeleteAcceptDataZj: '/AcceptDataZj/DeleteAcceptDataZj',
  /** 获取造价咨询接收资料管理列表 */
  GetAcceptDataZjList: '/AcceptDataZj/GetAcceptDataZjList',
  /** 修改造价咨询接收资料管理 */
  UpdateAcceptDataZj: '/AcceptDataZj/UpdateAcceptDataZj',
  /** 修改排序 */
  UpdateAcceptDataZjSort: '/AcceptDataZj/UpdateAcceptDataZjSort',
}
export const apiAutoAddress = {
  /** 获取地址 */
  GetAddressList: '/Address/GetAddressList',
  /** 获取区 */
  GetAreaList: '/Address/GetAreaList',
  /** 获取市 */
  GetCityList: '/Address/GetCityList',
  /** 获取省 */
  GetProvinceList: '/Address/GetProvinceList',
}
export const apiAutoAdvisoryOpinion = {
  /** 新增咨询意见 */
  AddAdvisoryOpinion: '/AdvisoryOpinion/AddAdvisoryOpinion',
  /** 生成咨询意见编码 */
  CreateAdvisoryOpinionCoding: '/AdvisoryOpinion/CreateAdvisoryOpinionCoding',
  /** 删除咨询意见 */
  DeleteAdvisoryOpinion: '/AdvisoryOpinion/DeleteAdvisoryOpinion',
  /** 咨询意见归档 */
  GdAdvisoryOpinion: '/AdvisoryOpinion/GdAdvisoryOpinion',
  /** 获取咨询意见列表 */
  GetAdvisoryOpinionList: '/AdvisoryOpinion/GetAdvisoryOpinionList',
  /** 获取咨询意见列表统计 */
  GetAdvisoryOpinionListCount: '/AdvisoryOpinion/GetAdvisoryOpinionListCount',
  /** 获取咨询意见列表统计送审，审定，实付 */
  GetAdvisoryOpinionListCountMoney: '/AdvisoryOpinion/GetAdvisoryOpinionListCountMoney',
  /** 获取咨询意见列表统计--新 */
  GetAdvisoryOpinionListCountNew: '/AdvisoryOpinion/GetAdvisoryOpinionListCountNew',
  /** 获取咨询意见--单条 */
  GetAdvisoryOpinionSingle: '/AdvisoryOpinion/GetAdvisoryOpinionSingle',
  /** 作废咨询意见 */
  IsCancelAdvisoryOpinion: '/AdvisoryOpinion/IsCancelAdvisoryOpinion',
  /** 修改咨询意见 */
  UpdateAdvisoryOpinion: '/AdvisoryOpinion/UpdateAdvisoryOpinion',
  /** 修改咨询意见主表信息 */
  UpdateAdvisoryOpinionInfo: '/AdvisoryOpinion/UpdateAdvisoryOpinionInfo',
  /** 修改报告号 */
  UpdateReportNoAdvisoryOpinion: '/AdvisoryOpinion/UpdateReportNoAdvisoryOpinion',
}
export const apiAutoAdvisoryOpinionFile = {
  /** 新增咨询意见文件 */
  AddAdvisoryOpinionFile: '/AdvisoryOpinionFile/AddAdvisoryOpinionFile',
  /** 批量新增咨询意见文件 */
  AddAdvisoryOpinionFileBath: '/AdvisoryOpinionFile/AddAdvisoryOpinionFileBath',
  /** 新增全过程咨询-咨询意见文件 */
  AddJlCommonDocumentPmAdvisoryOpinionFile: '/AdvisoryOpinionFile/AddJlCommonDocumentPmAdvisoryOpinionFile',
  /** 批量新增全过程咨询-咨询意见文件 */
  AddJlCommonDocumentPmAdvisoryOpinionFileBath: '/AdvisoryOpinionFile/AddJlCommonDocumentPmAdvisoryOpinionFileBath',
  /** 删除咨询意见文件 */
  DeleteAdvisoryOpinionFile: '/AdvisoryOpinionFile/DeleteAdvisoryOpinionFile',
  /** 删除全过程咨询-咨询意见文件 */
  DeleteJlCommonDocumentPmAdvisoryOpinionFile: '/AdvisoryOpinionFile/DeleteJlCommonDocumentPmAdvisoryOpinionFile',
  /** 获取咨询意见文件列表 */
  GetAdvisoryOpinionFileList: '/AdvisoryOpinionFile/GetAdvisoryOpinionFileList',
  /** 获取全过程咨询-咨询意见文件列表 */
  GetJlCommonDocumentPmAdvisoryOpinionFileList: '/AdvisoryOpinionFile/GetJlCommonDocumentPmAdvisoryOpinionFileList',
  /** 修改咨询意见文件 */
  UpdateAdvisoryOpinionFile: '/AdvisoryOpinionFile/UpdateAdvisoryOpinionFile',
}
export const apiAutoAdvisoryOpinionOverprint = {
  /** 意见增印撤回 */
  AdvisoryOpinionOverprintReCall: '/AdvisoryOpinionOverprint/AdvisoryOpinionOverprintReCall',
  /** 删除咨询意见增印 */
  DeleteAdvisoryOpinionOverprint: '/AdvisoryOpinionOverprint/DeleteAdvisoryOpinionOverprint',
  /** 获取咨询意见增印列表 */
  GetAdvisoryOpinionOverprintList: '/AdvisoryOpinionOverprint/GetAdvisoryOpinionOverprintList',
  /** 新增咨询意见增印 */
  SubmitAdvisoryOpinionOverprint: '/AdvisoryOpinionOverprint/SubmitAdvisoryOpinionOverprint',
}
export const apiAutoAdvisoryOpinionPer = {
  /** 新增咨询意见人员 */
  AddAdvisoryOpinionPer: '/AdvisoryOpinionPer/AddAdvisoryOpinionPer',
  /** 删除咨询意见人员 */
  DeleteAdvisoryOpinionPer: '/AdvisoryOpinionPer/DeleteAdvisoryOpinionPer',
  /** 获取咨询意见复核 */
  GetAdvisoryOpinionFhList: '/AdvisoryOpinionPer/GetAdvisoryOpinionFhList',
  /** 获取咨询意见人员列表 */
  GetAdvisoryOpinionPerList: '/AdvisoryOpinionPer/GetAdvisoryOpinionPerList',
  /** 移动端 审批统计 */
  GetExamineCount: '/AdvisoryOpinionPer/GetExamineCount',
  /** 复核统计 */
  GetFhCount: '/AdvisoryOpinionPer/GetFhCount',
  /** 获取咨询意见人员列表--视图 */
  GetVAdvisoryOpinionPerList: '/AdvisoryOpinionPer/GetVAdvisoryOpinionPerList',
  /** 获取咨询意见人员单条--视图 */
  GetVAdvisoryOpinionPerSingle: '/AdvisoryOpinionPer/GetVAdvisoryOpinionPerSingle',
  /** 审批不通过 */
  NoPassAdvisoryOpinionPer: '/AdvisoryOpinionPer/NoPassAdvisoryOpinionPer',
  /** 审批不通过--三级复核 */
  NoPassAdvisoryOpinionPerTh: '/AdvisoryOpinionPer/NoPassAdvisoryOpinionPerTh',
  /** 审批通过 */
  PassAdvisoryOpinionPer: '/AdvisoryOpinionPer/PassAdvisoryOpinionPer',
  /** 审批通过--三级复核 */
  PassAdvisoryOpinionPerTh: '/AdvisoryOpinionPer/PassAdvisoryOpinionPerTh',
  /** 员工主动撤回咨询意见复核 */
  StaffRecallAdvisoryOpinionPer: '/AdvisoryOpinionPer/StaffRecallAdvisoryOpinionPer',
  /** 修改咨询意见人员 */
  UpdateAdvisoryOpinionPer: '/AdvisoryOpinionPer/UpdateAdvisoryOpinionPer',
  /** 复核撤回 */
  recallAdvisoryOpinionPer: '/AdvisoryOpinionPer/recallAdvisoryOpinionPer',
  /** 审批撤回--三级复核 */
  recallAdvisoryOpinionPerTh: '/AdvisoryOpinionPer/recallAdvisoryOpinionPerTh',
}
export const apiAutoAdvisoryOpinionQuestion = {
  /** 新增咨询意见问题 */
  AddAdvisoryOpinionQuestion: '/AdvisoryOpinionQuestion/AddAdvisoryOpinionQuestion',
  /** 删除咨询意见问题 */
  DeleteAdvisoryOpinionQuestion: '/AdvisoryOpinionQuestion/DeleteAdvisoryOpinionQuestion',
  /** 获取最后一条复核问题 */
  GetAdvisoryOpinionQuestionFinalSingle: '/AdvisoryOpinionQuestion/GetAdvisoryOpinionQuestionFinalSingle',
  /** 获取咨询意见问题列表 */
  GetAdvisoryOpinionQuestionList: '/AdvisoryOpinionQuestion/GetAdvisoryOpinionQuestionList',
  /** 修改咨询意见问题 */
  UpdateAdvisoryOpinionQuestion: '/AdvisoryOpinionQuestion/UpdateAdvisoryOpinionQuestion',
  /** 修改是否作为项目提醒 */
  UpdateAdvisoryOpinionQuestionAoqIsProRemind: '/AdvisoryOpinionQuestion/UpdateAdvisoryOpinionQuestionAoqIsProRemind',
}
export const apiAutoAdvisoryOpinionUpdate = {
  /** 新增咨询意见变更 */
  AddAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/AddAdvisoryOpinionUpdate',
  /** 咨询意见变更撤回 */
  AdvisoryOpinionUpdateReCall: '/AdvisoryOpinionUpdate/AdvisoryOpinionUpdateReCall',
  /** 咨询意见变更提交审批 */
  AdvisoryOpinionUpdateSubmit: '/AdvisoryOpinionUpdate/AdvisoryOpinionUpdateSubmit',
  /** 咨询意见变更判断 */
  AoReCallJudge: '/AdvisoryOpinionUpdate/AoReCallJudge',
  /** 删除咨询意见变更 */
  DeleteAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/DeleteAdvisoryOpinionUpdate',
  /** 审批通过咨询意见变更 */
  ExamineAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/ExamineAdvisoryOpinionUpdate',
  /** 获取咨询意见变更列表 */
  GetAdvisoryOpinionUpdateList: '/AdvisoryOpinionUpdate/GetAdvisoryOpinionUpdateList',
  /** 获取咨询意见变更--单条 */
  GetAdvisoryOpinionUpdateSingle: '/AdvisoryOpinionUpdate/GetAdvisoryOpinionUpdateSingle',
  /** 咨询意见变更统计 */
  GetAouUpdateCount: '/AdvisoryOpinionUpdate/GetAouUpdateCount',
  /** 作废咨询意见变更 */
  IsCancelAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/IsCancelAdvisoryOpinionUpdate',
  /** 审批不通过咨询意见变更 */
  UnExamineAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/UnExamineAdvisoryOpinionUpdate',
  /** 修改咨询意见变更 */
  UpdateAdvisoryOpinionUpdate: '/AdvisoryOpinionUpdate/UpdateAdvisoryOpinionUpdate',
}
export const apiAutoAgreedTimeConfig = {
  /** 新增约定时间配置 */
  AddAgreedTimeConfig: '/AgreedTimeConfig/AddAgreedTimeConfig',
  /** 删除约定时间配置 */
  DeleteAgreedTimeConfig: '/AgreedTimeConfig/DeleteAgreedTimeConfig',
  /** 获取约定时间配置列表 */
  GetAgreedTimeConfigList: '/AgreedTimeConfig/GetAgreedTimeConfigList',
  /** 获取约定时间配置单个 */
  GetAgreedTimeConfigSingle: '/AgreedTimeConfig/GetAgreedTimeConfigSingle',
  /** 修改约定时间配置 */
  UpdateAgreedTimeConfig: '/AgreedTimeConfig/UpdateAgreedTimeConfig',
}
export const apiAutoAgreement = {
  /** 新增标价合同 */
  AddAgreement: '/Agreement/AddAgreement',
  /** 删除标价合同 */
  DeleteAgreement: '/Agreement/DeleteAgreement',
  /** 获取标价合同列表 */
  GetAgreementList: '/Agreement/GetAgreementList',
  /** 获取标价合同--单条 */
  GetAgreementSingle: '/Agreement/GetAgreementSingle',
  /** 获取标段合同咨询意见、员工日报、用章申请数量 */
  GetAgreementTabCount: '/Agreement/GetAgreementTabCount',
  /** 获取标段合同预付款列表 */
  GetPmAgreementAdvanceChargeList: '/Agreement/GetPmAgreementAdvanceChargeList',
  /** 进度款  联系单  咨询意见  跟踪日志  统计   by 标段合同Id */
  QgczCountByAg: '/Agreement/QgczCountByAg',
  /** 修改标价合同 */
  UpdateAgreement: '/Agreement/UpdateAgreement',
}
export const apiAutoAgreementPer = {
  /** 新增标段合同人员 */
  AddAgreementPer: '/AgreementPer/AddAgreementPer',
  /** 批量新增标段合同人员 */
  BathAddAgreementPer: '/AgreementPer/BathAddAgreementPer',
  /** 删除标段合同人员 */
  DeleteAgreementPer: '/AgreementPer/DeleteAgreementPer',
  /** 获取标段合同人员列表 */
  GetAgreementPerList: '/AgreementPer/GetAgreementPerList',
  /** 获取项目管理项目用户单条--登录用户 */
  GetLoginQgczxProjectUserSingle: '/AgreementPer/GetLoginQgczxProjectUserSingle',
  /** 修改标段合同人员 */
  UpdateAgreementPer: '/AgreementPer/UpdateAgreementPer',
}
export const apiAutoAliyunFunc = {
  /** 取消oss处理任务 */
  CancelAliyunFuncOssJob: '/AliyunFunc/CancelAliyunFuncOssJob',
  /** 删除oss处理任务 */
  DeleteAliyunFuncOssJob: '/AliyunFunc/DeleteAliyunFuncOssJob',
  /** 获取oss处理任务 */
  GetAliyunFuncOssJobList: '/AliyunFunc/GetAliyunFuncOssJobList',
  /** 获取oss处理任务 */
  GetAliyunFuncOssJobListCount: '/AliyunFunc/GetAliyunFuncOssJobListCount',
  /** 触发oss处理任务 */
  TriggerAliyunFuncOssJob: '/AliyunFunc/TriggerAliyunFuncOssJob',
}
export const apiAutoAliyunOss = {
  /** 转化为PDF */
  ChangePdf: '/AliyunOss/ChangePdf',
  /** 获取阿里云配置 */
  Config: '/AliyunOss/Config',
  /** 使用STS临时访问凭证访问OSS */
  GetAliyunStsCredentials: '/AliyunOss/GetAliyunStsCredentials',
  /** 广联达保存文件 */
  GldSaveFileUpdate: '/AliyunOss/GldSaveFileUpdate',
  /** 保存文件 */
  SaveFileUpdate: '/AliyunOss/SaveFileUpdate',
  /** 保存文件--替换 */
  SaveFileUpdateTh: '/AliyunOss/SaveFileUpdateTh',
  /** 保存文件 */
  SaveFileUpdateV2: '/AliyunOss/SaveFileUpdateV2',
  /** 保存文件--WPS */
  SaveFileUpdateWps: '/AliyunOss/SaveFileUpdateWps',
  /** 保存文件 */
  SaveFirstAoZxyjsFile: '/AliyunOss/SaveFirstAoZxyjsFile',
}
export const apiAutoAoEmployeeType = {
  /** 新增员工类型管理 */
  CreateAoEmployeeType: '/AoEmployeeType/CreateAoEmployeeType',
  /** 删除员工类型管理 */
  DeleteAoEmployeeType: '/AoEmployeeType/DeleteAoEmployeeType',
  /** 获取员工类型管理列表 */
  GetAoEmployeeType: '/AoEmployeeType/GetAoEmployeeType',
  /** 修改员工类型管理 */
  UpdateAoEmployeeType: '/AoEmployeeType/UpdateAoEmployeeType',
}
export const apiAutoAoOperationLog = {
  /** 获取咨询意见操作日志 */
  GetAoOperationLogList: '/AoOperationLog/GetAoOperationLogList',
}
export const apiAutoAoProfessionalType = {
  /** 新增专业类型管理 */
  CreateAoProfessionalType: '/AoProfessionalType/CreateAoProfessionalType',
  /** 删除专业类型管理 */
  DeleteAoProfessionalType: '/AoProfessionalType/DeleteAoProfessionalType',
  /** 获取专业类型管理列表 */
  GetAoProfessionalType: '/AoProfessionalType/GetAoProfessionalType',
  /** 修改专业类型管理 */
  UpdateAoProfessionalType: '/AoProfessionalType/UpdateAoProfessionalType',
}
export const apiAutoAoProjectScale = {
  /** 新增规模管理 */
  CreateAoProjectScale: '/AoProjectScale/CreateAoProjectScale',
  /** 删除规模管理 */
  DeleteAoProjectScale: '/AoProjectScale/DeleteAoProjectScale',
  /** 获取规模管理列表 */
  GetAoProjectScale: '/AoProjectScale/GetAoProjectScale',
  /** 修改规模管理 */
  UpdateAoProjectScale: '/AoProjectScale/UpdateAoProjectScale',
}
export const apiAutoAoRevenueType = {
  /** 新增创收类型管理 */
  CreateAoRevenueType: '/AoRevenueType/CreateAoRevenueType',
  /** 删除创收类型管理 */
  DeleteAoRevenueType: '/AoRevenueType/DeleteAoRevenueType',
  /** 获取创收类型管理列表 */
  GetAoRevenueType: '/AoRevenueType/GetAoRevenueType',
  /** 修改创收类型管理 */
  UpdateAoRevenueType: '/AoRevenueType/UpdateAoRevenueType',
}
export const apiAutoAoStageConfig = {
  /** 新增咨询意见阶段 */
  AddAoStageConfig: '/AoStageConfig/AddAoStageConfig',
  /** 删除咨询意见阶段 */
  DeleteAoStageConfig: '/AoStageConfig/DeleteAoStageConfig',
  /** 获取咨询意见阶段列表 */
  GetAoStageConfigList: '/AoStageConfig/GetAoStageConfigList',
  /** 修改咨询意见阶段 */
  UpdateAoStageConfig: '/AoStageConfig/UpdateAoStageConfig',
}
export const apiAutoAoTypeConfig = {
  /** 新增咨询意见类型 */
  AddAoTypeConfig: '/AoTypeConfig/AddAoTypeConfig',
  /** 删除咨询意见类型 */
  DeleteAoTypeConfig: '/AoTypeConfig/DeleteAoTypeConfig',
  /** 获取咨询意见类型列表 */
  GetAoTypeConfigList: '/AoTypeConfig/GetAoTypeConfigList',
  /** 修改咨询意见类型 */
  UpdateAoTypeConfig: '/AoTypeConfig/UpdateAoTypeConfig',
}
export const apiAutoArticle = {
  /** 新增文档 */
  AddArticle: '/Article/AddArticle',
  /** 删除文档 */
  DeleteArticle: '/Article/DeleteArticle',
  /** 获取文档列表 */
  GetArticleList: '/Article/GetArticleList',
  /** 获取单个文档 */
  GetArticleSingle: '/Article/GetArticleSingle',
  /** 修改文档 */
  UpdateArticle: '/Article/UpdateArticle',
  /** 修改文档--阅读量 */
  UpdateArticleReadNum: '/Article/UpdateArticleReadNum',
}
export const apiAutoArticleAccept = {
  /** 新增收文 */
  AddArticleAccept: '/ArticleAccept/AddArticleAccept',
  /** 选择 */
  ChoiceAccept: '/ArticleAccept/ChoiceAccept',
  /** 删除收文 */
  DeleteArticleAccept: '/ArticleAccept/DeleteArticleAccept',
  /** 审批通过 */
  ExamineArticleAccept: '/ArticleAccept/ExamineArticleAccept',
  /** 获取收入管理-传阅人表 */
  GetAaCyPerList: '/ArticleAccept/GetAaCyPerList',
  /** 获取收文列表 */
  GetArticleAcceptList: '/ArticleAccept/GetArticleAcceptList',
  /** 获取收文列表Count */
  GetArticleAcceptListCount: '/ArticleAccept/GetArticleAcceptListCount',
  /** 获取收文单条 */
  GetArticleAcceptSingle: '/ArticleAccept/GetArticleAcceptSingle',
  /** 点审核是判断状态是否已审核 */
  JudgeArticleAccept: '/ArticleAccept/JudgeArticleAccept',
  /** 创建人待审批状态撤回待提交 */
  RecallArticleAcceptDtj: '/ArticleAccept/RecallArticleAcceptDtj',
  /** 提交收入管理-传阅人表 */
  SubmitAaCyPer: '/ArticleAccept/SubmitAaCyPer',
  /** 提交审批 */
  SubmitArticleAccept: '/ArticleAccept/SubmitArticleAccept',
  /** 审批不通过 */
  UnExamineArticleAccept: '/ArticleAccept/UnExamineArticleAccept',
  /** 暂存收入管理-传阅人表 */
  UpdateAaCyPer: '/ArticleAccept/UpdateAaCyPer',
  /** 修改收文 */
  UpdateArticleAccept: '/ArticleAccept/UpdateArticleAccept',
}
export const apiAutoArticlePublish = {
  /** 新增发文 */
  AddArticlePublish: '/ArticlePublish/AddArticlePublish',
  /** 删除发文 */
  DeleteArticlePublish: '/ArticlePublish/DeleteArticlePublish',
  /** 审批通过 */
  ExamineArticlePublish: '/ArticlePublish/ExamineArticlePublish',
  /** 获取发文列表 */
  GetArticlePublishList: '/ArticlePublish/GetArticlePublishList',
  /** 获取发文列表Count */
  GetArticlePublishListCount: '/ArticlePublish/GetArticlePublishListCount',
  /** 获取发文单条 */
  GetArticlePublishSingle: '/ArticlePublish/GetArticlePublishSingle',
  /** 提交审批 */
  SubmitArticlePublish: '/ArticlePublish/SubmitArticlePublish',
  /** 审批不通过 */
  UnExamineArticlePublish: '/ArticlePublish/UnExamineArticlePublish',
  /** 修改发文 */
  UpdateArticlePublish: '/ArticlePublish/UpdateArticlePublish',
}
export const apiAutoArticleType = {
  /** 新增文档类型 */
  AddArticleType: '/ArticleType/AddArticleType',
  /** 删除文档类型 */
  DeleteArticleType: '/ArticleType/DeleteArticleType',
  /** 获取文档类型列表 */
  GetArticleTypeList: '/ArticleType/GetArticleTypeList',
  /** 修改文档类型 */
  UpdateArticleType: '/ArticleType/UpdateArticleType',
}
export const apiAutoAuditItem = {
  /** 新增跟踪审计项目 */
  AddAuditItem: '/AuditItem/AddAuditItem',
  /** 删除跟踪审计项目 */
  DeleteAuditItem: '/AuditItem/DeleteAuditItem',
  /** 获取跟踪审计项目列表 */
  GetAuditItemList: '/AuditItem/GetAuditItemList',
  /** 获取跟踪审计项目单条 */
  GetAuditItemSingle: '/AuditItem/GetAuditItemSingle',
  /** 修改跟踪审计项目 */
  UpdateAuditItem: '/AuditItem/UpdateAuditItem',
}
export const apiAutoAuditItemDep = {
  /** 新增意见反馈 */
  AddAuditItemDep: '/AuditItemDep/AddAuditItemDep',
  /** 删除意见反馈 */
  DeleteAuditItemDep: '/AuditItemDep/DeleteAuditItemDep',
  /** 获取意见反馈列表 */
  GetAuditItemDepList: '/AuditItemDep/GetAuditItemDepList',
  /** 修改意见反馈 */
  UpdateAuditItemDep: '/AuditItemDep/UpdateAuditItemDep',
}
export const apiAutoBankAccountStatement = {
  /** 新增银行对账单 */
  AddBankAccountStatement: '/BankAccountStatement/AddBankAccountStatement',
  /** 删除银行对账单 */
  DeleteBankAccountStatement: '/BankAccountStatement/DeleteBankAccountStatement',
  /** 获取银行对账单Count */
  GetBankAccountStatementCount: '/BankAccountStatement/GetBankAccountStatementCount',
  /** 获取银行对账单列表 */
  GetBankAccountStatementList: '/BankAccountStatement/GetBankAccountStatementList',
  /** 修改银行对账单 */
  UpdateBankAccountStatement: '/BankAccountStatement/UpdateBankAccountStatement',
  /** 导入联合模板 */
  UploadBankAccountStatementLh: '/BankAccountStatement/UploadBankAccountStatementLh',
  /** 导入浦发模板 */
  UploadBankAccountStatementPf: '/BankAccountStatement/UploadBankAccountStatementPf',
  /** 导入浙商模板 */
  UploadBankAccountStatementZs: '/BankAccountStatement/UploadBankAccountStatementZs',
}
export const apiAutoBankInfo = {
  /** 新增单位银行信息 */
  AddBankInfo: '/BankInfo/AddBankInfo',
  /** 删除单位银行信息 */
  DeleteBankInfo: '/BankInfo/DeleteBankInfo',
  /** 获取单位银行信息列表 */
  GetBankInfoList: '/BankInfo/GetBankInfoList',
  /** 修改单位银行信息 */
  UpdateBankInfo: '/BankInfo/UpdateBankInfo',
}
export const apiAutoBaseConfig = {
  /** 新增或修改基础开发配置 */
  AddOrUpdateBaseConfig: '/BaseConfig/AddOrUpdateBaseConfig',
  /** 新增或修改开关配置 */
  AddOrUpdateSwitchConfig: '/BaseConfig/AddOrUpdateSwitchConfig',
  /** 删除基础开发配置 */
  DeleteBaseConfig: '/BaseConfig/DeleteBaseConfig',
  /** 获取基础开发配置单条 */
  GetBaseConfigSingle: '/BaseConfig/GetBaseConfigSingle',
  /** 获取咨询意见类型配置列表 */
  GetCameraCompanyConfigList: '/BaseConfig/GetCameraCompanyConfigList',
  /** 获取基础开发配置单条--钉钉key */
  GetDdKey: '/BaseConfig/GetDdKey',
  /** 获取基础开发配置单条--钉钉key APP */
  GetDdKeyApp: '/BaseConfig/GetDdKeyApp',
  /** 获取基础开发配置单条--钉钉key移动端 */
  GetDdKeyMobile: '/BaseConfig/GetDdKeyMobile',
}
export const apiAutoBiaoXun = {
  /** 导出标讯数据 */
  ExportBiaoXunList: '/BiaoXun/ExportBiaoXunList',
  /** 获取标讯列表 */
  GetBiaoXunList: '/BiaoXun/GetBiaoXunList',
  /** 获取标讯单条 */
  GetBiaoXunSingle: '/BiaoXun/GetBiaoXunSingle',
  /** 获取大类列表 */
  GetLargeTypeConfigList: '/BiaoXun/GetLargeTypeConfigList',
  /** 获取来源列表 */
  GetSourceConfigList: '/BiaoXun/GetSourceConfigList',
  /** 获取类型列表 */
  GetTypeConfigList: '/BiaoXun/GetTypeConfigList',
  /** 获取地区列表 */
  GetAddressConfigList: '/fa',
}
export const apiAutoBidBond = {
  /** 新增保证金 */
  AddBidBond: '/BidBond/AddBidBond',
  /** 保证金回款 */
  BackMoneyBidBond: '/BidBond/BackMoneyBidBond',
  /** 保证金提交审批 */
  BidBondSubmit: '/BidBond/BidBondSubmit',
  /** 作废保证金 */
  CancelBidBond: '/BidBond/CancelBidBond',
  /** 删除保证金 */
  DeleteBidBond: '/BidBond/DeleteBidBond',
  /** 保证金审批通过 */
  ExamineBidBond: '/BidBond/ExamineBidBond',
  /** 保证金统计 */
  GetBidBondCount: '/BidBond/GetBidBondCount',
  /** 获取保证金列表 */
  GetBidBondList: '/BidBond/GetBidBondList',
  /** 获取保证金---单条 */
  GetBidBondSingle: '/BidBond/GetBidBondSingle',
  /** 保证金支付 */
  PayBidBond: '/BidBond/PayBidBond',
  /** 保证金审批不通过 */
  UnExamineBidBond: '/BidBond/UnExamineBidBond',
  /** 修改保证金 */
  UpdateBidBond: '/BidBond/UpdateBidBond',
}
export const apiAutoBidSection = {
  /** 新增标段 */
  AddBidSection: '/BidSection/AddBidSection',
  /** 删除标段 */
  DeleteBidSection: '/BidSection/DeleteBidSection',
  /** 获取标段列表 */
  GetBidSectionList: '/BidSection/GetBidSectionList',
  /** 修改标段 */
  UpdateBidSection: '/BidSection/UpdateBidSection',
}
export const apiAutoBiddingAgencyOperationlog = {
  /** 获取招标代理项目操作日志 */
  GetBiddingAgencyOperationlogList: '/BiddingAgencyOperationlog/GetBiddingAgencyOperationlogList',
  /** 获取投标项目操作日志 */
  GetBiddingOperationOperationlogList: '/BiddingAgencyOperationlog/GetBiddingOperationOperationlogList',
}
export const apiAutoBiddingPlatform = {
  /** 新增招标平台 */
  AddBiddingPlatform: '/BiddingPlatform/AddBiddingPlatform',
  /** 删除招标平台 */
  DeleteBiddingPlatform: '/BiddingPlatform/DeleteBiddingPlatform',
  /** 获取招标平台列表 */
  GetBiddingPlatformList: '/BiddingPlatform/GetBiddingPlatformList',
  /** 修改招标平台 */
  UpdateBiddingPlatform: '/BiddingPlatform/UpdateBiddingPlatform',
}
export const apiAutoBigscreenConfig = {
  /** 咨询意见整体统计 */
  GeAdvisoryOpinionWholeCount: '/BigscreenConfig/GeAdvisoryOpinionWholeCount',
  /** 跟踪日志整体统计 */
  GeAuditItemWholeCount: '/BigscreenConfig/GeAuditItemWholeCount',
  /** 咨询意见关联类型统计数量、送审价、审定价、核增核减率 */
  GetAdvisoryOpinionReTypeCountNumMoneyRate: '/BigscreenConfig/GetAdvisoryOpinionReTypeCountNumMoneyRate',
  /** 咨询阶段统计 */
  GetAdvisoryOpinionTypeCountNum: '/BigscreenConfig/GetAdvisoryOpinionTypeCountNum',
  /** 跟踪审计各类型日志数量统计 */
  GetAuditItemFollowLogTypeCount: '/BigscreenConfig/GetAuditItemFollowLogTypeCount',
  /** 获取大屏配置列表 */
  GetBigscreenConfigList: '/BigscreenConfig/GetBigscreenConfigList',
  /** 保证金统计 by bbtype */
  GetDepositByBbTypeCount: '/BigscreenConfig/GetDepositByBbTypeCount',
  /** 保证金统计 by dep */
  GetDepositByDepCount: '/BigscreenConfig/GetDepositByDepCount',
  /** 保证金统计 by paytype */
  GetDepositByPayTypeCount: '/BigscreenConfig/GetDepositByPayTypeCount',
  /** 保证金统计 */
  GetDepositWholeCount: '/BigscreenConfig/GetDepositWholeCount',
  /** 项目平均统计按月 */
  GetProjectByMonthCount: '/BigscreenConfig/GetProjectByMonthCount',
  /** 项目数量统计--by 部门 */
  GetProjectCountByDep: '/BigscreenConfig/GetProjectCountByDep',
  /** 项目状态统计 */
  GetProjectStateCount: '/BigscreenConfig/GetProjectStateCount',
  /** 项目类型统计 */
  GetProjectTypeCount: '/BigscreenConfig/GetProjectTypeCount',
  /** 项目整体统计 */
  GetProjectWholeCount: '/BigscreenConfig/GetProjectWholeCount',
  /** 项目类型统计 */
  GetYcProjectTypeCount: '/BigscreenConfig/GetYcProjectTypeCount',
  /** 开票回款统计  by  开票账号 */
  InvoiceBackByAccountCount: '/BigscreenConfig/InvoiceBackByAccountCount',
  /** 开票回款统计 */
  InvoiceBackCount: '/BigscreenConfig/InvoiceBackCount',
  /** 开票回款统计  by  部门 */
  InvoiceBackCountByDep: '/BigscreenConfig/InvoiceBackCountByDep',
  /** 回款业务类型统计 */
  InvoiceBackTypeCount: '/BigscreenConfig/InvoiceBackTypeCount',
  /** 开票统计--取20条 */
  InvoiceCountByKpName: '/BigscreenConfig/InvoiceCountByKpName',
  /** 开票部门统计 */
  InvoiceDepCount: '/BigscreenConfig/InvoiceDepCount',
  /** 开票业务类型统计 */
  InvoiceKpTypeCount: '/BigscreenConfig/InvoiceKpTypeCount',
  /** 开票类型统计 */
  InvoiceTypeCount: '/BigscreenConfig/InvoiceTypeCount',
  /** 项目，咨询意见，日志，工时总量统计 */
  ProAdoFollowWholeCount: '/BigscreenConfig/ProAdoFollowWholeCount',
  /** 修改大屏配置 */
  UpdateBigscreenConfig: '/BigscreenConfig/UpdateBigscreenConfig',
}
export const apiAutoBrand = {
  /** 新增品牌 */
  AddBrand: '/Brand/AddBrand',
  /** 删除品牌 */
  DeleteBrand: '/Brand/DeleteBrand',
  /** 获取品牌 */
  GetBrandList: '/Brand/GetBrandList',
  /** 修改品牌 */
  UpdateBrand: '/Brand/UpdateBrand',
  /** 批量修改品牌排序 */
  UpdateBrandSort: '/Brand/UpdateBrandSort',
}
export const apiAutoBusinessType = {
  /** 新增业务类型 */
  AddBusinessType: '/BusinessType/AddBusinessType',
  /** 删除业务类型 */
  DeleteBusinessType: '/BusinessType/DeleteBusinessType',
  /** 获取业务类型列表 */
  GetBusinessTypeList: '/BusinessType/GetBusinessTypeList',
  /** 获取业务类型单个 */
  GetBusinessTypeSingle: '/BusinessType/GetBusinessTypeSingle',
  /** 修改业务类型 */
  UpdateBusinessType: '/BusinessType/UpdateBusinessType',
}
export const apiAutoBxMemberTs = {
  /** 添加历史 */
  AddMemberHs: '/BxMemberTs/AddMemberHs',
  /** 添加收藏 */
  AddMemberSc: '/BxMemberTs/AddMemberSc',
  /** 添加推送用户 */
  AddMemberTs: '/BxMemberTs/AddMemberTs',
  /** 删除历史 */
  DelMemberHs: '/BxMemberTs/DelMemberHs',
  /** 取消收藏 */
  DelMemberSc: '/BxMemberTs/DelMemberSc',
  /** 删除推送用户 */
  DelMemberTs: '/BxMemberTs/DelMemberTs',
  /** 删除历史 */
  GetMemberHsistInAdmin: '/BxMemberTs/GetMemberHsistInAdmin',
  /** 收藏记录---我的 */
  GetMemberScistInAdmin: '/BxMemberTs/GetMemberScistInAdmin',
  /** 查询推送用户列表 */
  GetMemberTsListInAdmin: '/BxMemberTs/GetMemberTsListInAdmin',
  /** 查询单条推送用户 */
  GetSingleMemberTsInAdmin: '/BxMemberTs/GetSingleMemberTsInAdmin',
  /** 查询单条推送用户---根据OPENID */
  GetSingleMemberTsInWeb: '/BxMemberTs/GetSingleMemberTsInWeb',
  /** 查询用户推送记录 */
  GetTsRecordListInAdmin: '/BxMemberTs/GetTsRecordListInAdmin',
  /** 推送记录---我的 */
  GetTsRecordListInWeb: '/BxMemberTs/GetTsRecordListInWeb',
  /** 获取单条招标信息是否收藏 */
  MemberIsSc: '/BxMemberTs/MemberIsSc',
  /** 修改推送用户 */
  UpdateMemberTsAdmin: '/BxMemberTs/UpdateMemberTsAdmin',
  /** 修改推送用户 */
  UpdateMemberTsWeb: '/BxMemberTs/UpdateMemberTsWeb',
  /**  */
  ZhaoBiaoTs: '/BxMemberTs/ZhaoBiaoTs',
}
export const apiAutoCameraVersion = {
  /** 新增水印相机版本管理 */
  AddCameraVersion: '/CameraVersion/AddCameraVersion',
  /** 删除水印相机版本管理 */
  DeleteCameraVersion: '/CameraVersion/DeleteCameraVersion',
  /** 获取水印相机版本管理列表 */
  GetCameraVersionList: '/CameraVersion/GetCameraVersionList',
  /** 获取水印相机版本--app端 */
  GetCameraVersionSingle: '/CameraVersion/GetCameraVersionSingle',
  /** 修改水印相机版本管理 */
  UpdateCameraVersion: '/CameraVersion/UpdateCameraVersion',
}
export const apiAutoCertType = {
  /** 新增证书类型 */
  AddCertType: '/CertType/AddCertType',
  /** 删除证书类型 */
  DeleteCertType: '/CertType/DeleteCertType',
  /** 获取证书类型列表 */
  GetCertTypeList: '/CertType/GetCertTypeList',
  /** 修改证书类型 */
  UpdateCertType: '/CertType/UpdateCertType',
}
export const apiAutoCheckPer = {
  /** 新增二三级复核人员 */
  AddCheckPer: '/CheckPer/AddCheckPer',
  /** 删除二三级复核人员 */
  DeleteCheckPer: '/CheckPer/DeleteCheckPer',
  /** 获取二三级复核人员  BY DoId */
  GetCheckPerSingle: '/CheckPer/GetCheckPerSingle',
  /** 修改二三级复核人员 */
  UpdateCheckPer: '/CheckPer/UpdateCheckPer',
}
export const apiAutoCheckTemplate = {
  /** 新增复核问题 */
  AddCheckQuestion: '/CheckTemplate/AddCheckQuestion',
  /** 新增复核模板 */
  AddCheckTemplate: '/CheckTemplate/AddCheckTemplate',
  /** 新增复核问题集 */
  AddFhQuestionAdduct: '/CheckTemplate/AddFhQuestionAdduct',
  /** 删除复核问题 */
  DeleteCheckQuestion: '/CheckTemplate/DeleteCheckQuestion',
  /** 删除复核模板 */
  DeleteCheckTemplate: '/CheckTemplate/DeleteCheckTemplate',
  /** 删除复核问题集 */
  DeleteFhQuestionAdduct: '/CheckTemplate/DeleteFhQuestionAdduct',
  /** 获取复核问题列表 */
  GetCheckQuestionList: '/CheckTemplate/GetCheckQuestionList',
  /** 获取复核模板列表 */
  GetCheckTemplateList: '/CheckTemplate/GetCheckTemplateList',
  /** 获取复核问题集列表 */
  GetFhQuestionAdductList: '/CheckTemplate/GetFhQuestionAdductList',
  /** 修改复核问题 */
  UpdateCheckQuestion: '/CheckTemplate/UpdateCheckQuestion',
  /** 修改复核模板 */
  UpdateCheckTemplate: '/CheckTemplate/UpdateCheckTemplate',
  /** 修改复核问题集 */
  UpdateFhQuestionAdduct: '/CheckTemplate/UpdateFhQuestionAdduct',
}
export const apiAutoCiProTemplateNode = {
  /** 新增合同录入项目内模板节点 */
  AddCiProTemplateNode: '/CiProTemplateNode/AddCiProTemplateNode',
  /** 批量新增合同录入项目内模板节点 */
  BathAddCiProTemplateNode: '/CiProTemplateNode/BathAddCiProTemplateNode',
  /** 删除合同录入项目内模板节点 */
  DeleteCiProTemplateNode: '/CiProTemplateNode/DeleteCiProTemplateNode',
  /** 获取合同录入项目内模板节点列表 */
  GetCiProTemplateNodeList: '/CiProTemplateNode/GetCiProTemplateNodeList',
  /** 修改合同录入项目内模板节点 */
  UpdateCiProTemplateNode: '/CiProTemplateNode/UpdateCiProTemplateNode',
}
export const apiAutoCiShareGroup = {
  /** 新增共享创收单 */
  AddCiShareGroup: '/CiShareGroup/AddCiShareGroup',
  /** 删除共享创收单 */
  DeleteCiShareGroup: '/CiShareGroup/DeleteCiShareGroup',
  /** 获取共享创收单列表 */
  GetCiShareGroupList: '/CiShareGroup/GetCiShareGroupList',
  /** 导出 */
  GetCiShareGroupListExcel: '/CiShareGroup/GetCiShareGroupListExcel',
  /** 获取共享创收单单条 */
  GetCiShareGroupSingle: '/CiShareGroup/GetCiShareGroupSingle',
  /** 修改共享创收单 */
  UpdateCiShareGroup: '/CiShareGroup/UpdateCiShareGroup',
}
export const apiAutoCiShareGroupPer = {
  /** 新增共享创收单人员 */
  AddCiShareGroupPer: '/CiShareGroupPer/AddCiShareGroupPer',
  /** 添加共享创收单 */
  AddCiShareGroupPerCiNum: '/CiShareGroupPer/AddCiShareGroupPerCiNum',
  /** 移除已经共享的创收 */
  DelCiShareGroupPerCiNum: '/CiShareGroupPer/DelCiShareGroupPerCiNum',
  /** 删除共享创收单人员 */
  DeleteCiShareGroupPer: '/CiShareGroupPer/DeleteCiShareGroupPer',
  /** 获取共享创收单人员列表 */
  GetCiShareGroupPerList: '/CiShareGroupPer/GetCiShareGroupPerList',
  /** 修改共享创收单人员 */
  UpdateCiShareGroupPer: '/CiShareGroupPer/UpdateCiShareGroupPer',
}
export const apiAutoCiSkType = {
  /** 新增收款类型配置 */
  AddCiSkType: '/CiSkType/AddCiSkType',
  /** 删除收款类型配置 */
  DeleteCiSkType: '/CiSkType/DeleteCiSkType',
  /** 获取收款类型配置列表 */
  GetCiSkTypeList: '/CiSkType/GetCiSkTypeList',
  /** 获取收款类型配置单条 */
  GetCiSkTypeSingle: '/CiSkType/GetCiSkTypeSingle',
  /** 修改收款类型配置 */
  UpdateCiSkType: '/CiSkType/UpdateCiSkType',
}
export const apiAutoCodeScanningImg = {
  /** 新增扫码图片 */
  AddCodeScanningImg: '/CodeScanningImg/AddCodeScanningImg',
  /** 删除扫码图片 */
  DeleteCodeScanningImg: '/CodeScanningImg/DeleteCodeScanningImg',
  /** 获取扫码图片列表 */
  GetCodeScanningImgList: '/CodeScanningImg/GetCodeScanningImgList',
}
export const apiAutoCodingRules = {
  /** 新增编码规则 */
  AddCodingRules: '/CodingRules/AddCodingRules',
  /** 删除编码规则 */
  DeleteCodingRules: '/CodingRules/DeleteCodingRules',
  /** 获取合同编号--补充协议 */
  GetBcContractCoding: '/CodingRules/GetBcContractCoding',
  /** 获取编码规则列表--by 项目id */
  GetCodingRulesByPiIdList: '/CodingRules/GetCodingRulesByPiIdList',
  /** 获取编码规则列表 */
  GetCodingRulesList: '/CodingRules/GetCodingRulesList',
  /** 获取合同编号 */
  GetContractCoding: '/CodingRules/GetContractCoding',
  /** 修改编码规则 */
  UpdateCodingRules: '/CodingRules/UpdateCodingRules',
  /** 更新ct_year和ct_no_num的值--没有存档编号 */
  UpdateContract: '/CodingRules/UpdateContract',
  /** 提取合同号前半部分字符串 */
  UpdateContractNoStr: '/CodingRules/UpdateContractNoStr',
  /** 更新ct_year和ct_no_num的值--有存档编号 */
  UpdateContractWithCtNoId: '/CodingRules/UpdateContractWithCtNoId',
}
export const apiAutoCommissionAssessmentMethod = {
  /** 添加考核方法 */
  AddCommissionAssessmentMethod: '/CommissionAssessmentMethod/AddCommissionAssessmentMethod',
  /** 删除考核方法 */
  DeleteCommissionAssessmentMethod: '/CommissionAssessmentMethod/DeleteCommissionAssessmentMethod',
  /** 获取考核方法 */
  GetCommissionAssessmentMethodList: '/CommissionAssessmentMethod/GetCommissionAssessmentMethodList',
  /** 更新考核方法 */
  UpdateCommissionAssessmentMethod: '/CommissionAssessmentMethod/UpdateCommissionAssessmentMethod',
}
export const apiAutoCommissionChargeMajor = {
  /** 添加项目收费专业管理 */
  AddCommissionChargeMajor: '/CommissionChargeMajor/AddCommissionChargeMajor',
  /** 删除项目收费专业管理 */
  DeleteCommissionChargeMajor: '/CommissionChargeMajor/DeleteCommissionChargeMajor',
  /** 获取收费标准 */
  GetCommissionChargeMajordList: '/CommissionChargeMajor/GetCommissionChargeMajordList',
  /** 更新项目收费专业管理 */
  UpdateCommissionChargeMajor: '/CommissionChargeMajor/UpdateCommissionChargeMajor',
}
export const apiAutoCommissionChargeStandard = {
  /** 添加收费标准 */
  AddCommissionAssessmentMethod: '/CommissionChargeStandard/AddCommissionAssessmentMethod',
  /** 删除收费标准 */
  DeleteCommissionAssessmentMethod: '/CommissionChargeStandard/DeleteCommissionAssessmentMethod',
  /** 获取收费标准 */
  GetCommissionChargeStandardList: '/CommissionChargeStandard/GetCommissionChargeStandardList',
  /** 更新收费标准 */
  UpdateCommissionAssessmentMethod: '/CommissionChargeStandard/UpdateCommissionAssessmentMethod',
}
export const apiAutoCommissionRecord = {
  /** 新增提成记录 */
  AddCommissionRecord: '/CommissionRecord/AddCommissionRecord',
  /** 作废提成记录 */
  CancelCommissionRecord: '/CommissionRecord/CancelCommissionRecord',
  /** 删除提成记录 */
  DeleteCommissionRecord: '/CommissionRecord/DeleteCommissionRecord',
  /** 获取提成记录列表 */
  GetCommissionRecordList: '/CommissionRecord/GetCommissionRecordList',
  /** 获取提成记录单条 */
  GetCommissionRecordSingle: '/CommissionRecord/GetCommissionRecordSingle',
  /** 判断是否可以修改提成记录 */
  JudgeUpdateCommissionRecord: '/CommissionRecord/JudgeUpdateCommissionRecord',
  /** 修改提成记录 */
  UpdateCommissionRecord: '/CommissionRecord/UpdateCommissionRecord',
  /** 修改产值分配人员--提成确认提交 */
  UpdateOutputValuePerCommission: '/CommissionRecord/UpdateOutputValuePerCommission',
}
export const apiAutoCommissionType = {
  /** 添加提成项目类型 */
  AddCommissionType: '/CommissionType/AddCommissionType',
  /** 删除提成项目类型 */
  DeleteCommissionType: '/CommissionType/DeleteCommissionType',
  /** 获取提成项目类型 */
  GetCommissionTypeList: '/CommissionType/GetCommissionTypeList',
  /** 更新提成项目类型 */
  UpdateCommissionType: '/CommissionType/UpdateCommissionType',
}
export const apiAutoCommonAddressBook = {
  /** 新增公共通讯录 */
  AddCommonAddressBook: '/CommonAddressBook/AddCommonAddressBook',
  /** 复制公共通讯录 */
  CopyCommonAddressBook: '/CommonAddressBook/CopyCommonAddressBook',
  /** 删除公共通讯录 */
  DeleteCommonAddressBook: '/CommonAddressBook/DeleteCommonAddressBook',
  /** 获取公共通讯录列表 */
  GetCommonAddressBookList: '/CommonAddressBook/GetCommonAddressBookList',
  /** 修改公共通讯录 */
  UpdateCommonAddressBook: '/CommonAddressBook/UpdateCommonAddressBook',
}
export const apiAutoCommonLink = {
  /** 新增常用链接 */
  AddCommonLink: '/CommonLink/AddCommonLink',
  /** 删除常用链接 */
  DeleteCommonLink: '/CommonLink/DeleteCommonLink',
  /** 获取常用链接列表 */
  GetCommonLinkList: '/CommonLink/GetCommonLinkList',
  /** 修改常用链接 */
  UpdateCommonLink: '/CommonLink/UpdateCommonLink',
}
export const apiAutoCommonLinkType = {
  /** 新增常用链接类型 */
  AddCommonLinkType: '/CommonLinkType/AddCommonLinkType',
  /** 删除常用链接类型 */
  DeleteCommonLinkType: '/CommonLinkType/DeleteCommonLinkType',
  /** 获取常用链接类型列表 */
  GetCommonLinkTypeList: '/CommonLinkType/GetCommonLinkTypeList',
  /** 修改常用链接类型 */
  UpdateCommonLinkType: '/CommonLinkType/UpdateCommonLinkType',
}
export const apiAutoCommonQuestion = {
  /** 新增常见问题 */
  AddCommonQuestion: '/CommonQuestion/AddCommonQuestion',
  /** 删除常见问题 */
  DeleteCommonQuestion: '/CommonQuestion/DeleteCommonQuestion',
  /** 获取常见问题列表 */
  GetCommonQuestionList: '/CommonQuestion/GetCommonQuestionList',
  /** 获取常见问题列表 */
  GetCommonQuestionListCount: '/CommonQuestion/GetCommonQuestionListCount',
  /** 点赞或者取消点赞 */
  LikeOrNotCommonQuestion: '/CommonQuestion/LikeOrNotCommonQuestion',
  /** 修改常见问题 */
  UpdateCommonQuestion: '/CommonQuestion/UpdateCommonQuestion',
}
export const apiAutoCommunicateFile = {
  /** 新增沟通记录文件 */
  AddCommunicateFile: '/CommunicateFile/AddCommunicateFile',
  /** 批量新增沟通记录文件 */
  AddCommunicateFileBath: '/CommunicateFile/AddCommunicateFileBath',
  /** 删除沟通记录文件 */
  DeleteCommunicateFile: '/CommunicateFile/DeleteCommunicateFile',
  /** 获取沟通记录文件列表 */
  GetCommunicateFileList: '/CommunicateFile/GetCommunicateFileList',
  /** 修改沟通记录文件 */
  UpdateCommunicateFile: '/CommunicateFile/UpdateCommunicateFile',
}
export const apiAutoCompanyBank = {
  /** 新增公司银行配置 */
  AddCompanyBank: '/CompanyBank/AddCompanyBank',
  /** 删除公司银行配置 */
  DeleteCompanyBank: '/CompanyBank/DeleteCompanyBank',
  /** 获取公司银行配置列表 */
  GetCompanyBankList: '/CompanyBank/GetCompanyBankList',
  /** 修改公司银行配置 */
  UpdateCompanyBank: '/CompanyBank/UpdateCompanyBank',
}
export const apiAutoComprehensiveStatistics = {
  /** 造价咨询项目-列表 */
  GetProjectInfoList: '/ComprehensiveStatistics/GetProjectInfoList',
  /** 造价咨询项目-状态计数 */
  GetProjectInfoListStateCount: '/ComprehensiveStatistics/GetProjectInfoListStateCount',
  /** 造价咨询项目-汇总 */
  GetProjectInfoListSummary: '/ComprehensiveStatistics/GetProjectInfoListSummary',
}
export const apiAutoComputer = {
  /** 新增电脑 */
  AddComputer: '/Computer/AddComputer',
  /** 删除电脑 */
  DeleteComputer: '/Computer/DeleteComputer',
  /** 导出电脑数据 */
  ExportComputerList: '/Computer/ExportComputerList',
  /** 获取电脑列表 */
  GetComputerList: '/Computer/GetComputerList',
  /** 获取电脑单条 */
  GetComputerSingle: '/Computer/GetComputerSingle',
  /** 修改电脑 */
  UpdateComputer: '/Computer/UpdateComputer',
}
export const apiAutoConnect = {
  /**  */
  Token: '/Connect/Token',
}
export const apiAutoConsole = {
  /** 造价-逾期查询 */
  GetPlanCount: '/Console/GetPlanCount',
  /** 造价-逾期查询 */
  GetProjectInfoListForYq: '/Console/GetProjectInfoListForYq',
}
export const apiAutoContract = {
  /** 新增合同 */
  AddContract: '/Contract/AddContract',
  /** 合同作废 */
  CancelContract: '/Contract/CancelContract',
  /** 合同提交审批 */
  ContractSubmit: '/Contract/ContractSubmit',
  /** 删除合同 */
  DeleteContract: '/Contract/DeleteContract',
  /** 导出合同 */
  ExportContract: '/Contract/ExportContract',
  /** 导出合同业绩库 */
  ExportContractPerformance: '/Contract/ExportContractPerformance',
  /** 合同归档 */
  GdContract: '/Contract/GdContract',
  /** 合同提交归档 */
  GdSubmitContract: '/Contract/GdSubmitContract',
  /** 合同提交归档--退回 */
  GdSubmitContractBack: '/Contract/GdSubmitContractBack',
  /** 合同统计 */
  GetContractCount: '/Contract/GetContractCount',
  /** 获取合同兄弟和父亲列表 */
  GetContractFaAndBoList: '/Contract/GetContractFaAndBoList',
  /** 获取合同列表--框架合同 */
  GetContractKjList: '/Contract/GetContractKjList',
  /** 获取合同列表 */
  GetContractList: '/Contract/GetContractList',
  /** 获取合同关联项目统计 */
  GetContractReProjectCount: '/Contract/GetContractReProjectCount',
  /** 获取合同单条 */
  GetContractSingle: '/Contract/GetContractSingle',
  /** 获取合同单条--BY 盖章id */
  GetContractSingleByPusId: '/Contract/GetContractSingleByPusId',
  /** 获取首页合同列表 */
  GetIndexContractList: '/Contract/GetIndexContractList',
  /** 获取首页合同临近到期/已到期列表 */
  GetIndexLqYqContractList: '/Contract/GetIndexLqYqContractList',
  /** 获取单条项目业绩库--,合同,相关单位信息 */
  GetSingleContractInfoAchievement: '/Contract/GetSingleContractInfoAchievement',
  /** 合同是否结束 */
  IsEndContract: '/Contract/IsEndContract',
  /** 合同作废还原 */
  NoCancelContract: '/Contract/NoCancelContract',
  /** 撤回归档 */
  RecallGdContract: '/Contract/RecallGdContract',
  /** 修改合同 */
  UpdateContract: '/Contract/UpdateContract',
  /** 更新是否荣誉库 */
  UpdateContractIsHonorBank: '/Contract/UpdateContractIsHonorBank',
}
export const apiAutoContractFeatures = {
  /** 新增合同特征 */
  AddContractFeatures: '/ContractFeatures/AddContractFeatures',
  /** 删除合同特征 */
  DeleteContractFeatures: '/ContractFeatures/DeleteContractFeatures',
  /** 获取合同特征列表 */
  GetContractFeaturesList: '/ContractFeatures/GetContractFeaturesList',
  /** 修改合同特征 */
  UpdateContractFeatures: '/ContractFeatures/UpdateContractFeatures',
}
export const apiAutoContractFileLog = {
  /** 获取合同文档操作日志 */
  AddContractFileLog: '/ContractFileLog/AddContractFileLog',
  /**  */
  DealContractFileLog: '/ContractFileLog/DealContractFileLog',
  /**  */
  DealContractFileLogContent: '/ContractFileLog/DealContractFileLogContent',
  /** 添加合同文档操作日志 */
  GetContractFileLogList: '/ContractFileLog/GetContractFileLogList',
  /** 按中标项目金额 */
  GetProBiddingAgencyBiddingProMoneyCountList: '/ContractFileLog/GetProBiddingAgencyBiddingProMoneyCountList',
  /** 按中标项目数量 */
  GetProBiddingAgencyBiddingProNumCountList: '/ContractFileLog/GetProBiddingAgencyBiddingProNumCountList',
  /** 获取招标代理招标类型统计 */
  GetProBiddingAgencyBiddingTypeCountList: '/ContractFileLog/GetProBiddingAgencyBiddingTypeCountList',
  /** 获取招标代理投资类型统计 */
  GetProBiddingAgencyInvestTypeCountList: '/ContractFileLog/GetProBiddingAgencyInvestTypeCountList',
  /** 月度中标项目统计 */
  GetProBiddingAgencyMonthBiddingProCountList: '/ContractFileLog/GetProBiddingAgencyMonthBiddingProCountList',
  /** 按委托招标金额 */
  GetProBiddingAgencyWtBiddingProMoneyCountList: '/ContractFileLog/GetProBiddingAgencyWtBiddingProMoneyCountList',
  /** 按委托招标项目数量 */
  GetProBiddingAgencyWtBiddingProNumCountList: '/ContractFileLog/GetProBiddingAgencyWtBiddingProNumCountList',
  /** 获取招标代理项目列表统计 */
  GetProbiddingAgencyWholeCountList: '/ContractFileLog/GetProbiddingAgencyWholeCountList',
}
export const apiAutoContractInputTemplate = {
  /** 新增合同录入模板 */
  AddContractInputTemplate: '/ContractInputTemplate/AddContractInputTemplate',
  /** 删除合同录入模板 */
  DeleteContractInputTemplate: '/ContractInputTemplate/DeleteContractInputTemplate',
  /** 获取合同录入模板列表 */
  GetContractInputTemplateList: '/ContractInputTemplate/GetContractInputTemplateList',
  /** 修改合同录入模板 */
  UpdateContractInputTemplate: '/ContractInputTemplate/UpdateContractInputTemplate',
}
export const apiAutoContractInputTemplateNode = {
  /** 新增合同录入模板节点 */
  AddContractInputTemplateNode: '/ContractInputTemplateNode/AddContractInputTemplateNode',
  /** 批量新增合同录入模板节点 */
  BathAddContractInputTemplateNode: '/ContractInputTemplateNode/BathAddContractInputTemplateNode',
  /** 删除合同录入模板节点 */
  DeleteContractInputTemplateNode: '/ContractInputTemplateNode/DeleteContractInputTemplateNode',
  /** 获取合同录入模板节点列表 */
  GetContractInputTemplateNodeABtList: '/ContractInputTemplateNode/GetContractInputTemplateNodeABtList',
  /** 获取合同录入模板节点列表 */
  GetContractInputTemplateNodeList: '/ContractInputTemplateNode/GetContractInputTemplateNodeList',
  /** 修改合同录入模板节点 */
  UpdateContractInputTemplateNode: '/ContractInputTemplateNode/UpdateContractInputTemplateNode',
}
export const apiAutoContractInputType = {
  /** 新增合同录入类型字段表 */
  AddContractInputType: '/ContractInputType/AddContractInputType',
  /** 删除合同录入类型字段表 */
  DeleteContractInputType: '/ContractInputType/DeleteContractInputType',
  /** 获取合同录入类型字段表列表 */
  GetContractInputTypeList: '/ContractInputType/GetContractInputTypeList',
  /** 修改合同录入类型字段表 */
  UpdateContractInputType: '/ContractInputType/UpdateContractInputType',
}
export const apiAutoContractRange = {
  /** 新增合同范围 */
  AddContractRange: '/ContractRange/AddContractRange',
  /** 删除合同范围 */
  DeleteContractRange: '/ContractRange/DeleteContractRange',
  /** 获取合同范围列表 */
  GetContractRangeList: '/ContractRange/GetContractRangeList',
  /** 修改合同范围 */
  UpdateContractRange: '/ContractRange/UpdateContractRange',
}
export const apiAutoContractUpdate = {
  /** 新增合同变更 */
  AddContractUpdate: '/ContractUpdate/AddContractUpdate',
  /** 合同项目变更撤回 */
  ContractUpdateReCall: '/ContractUpdate/ContractUpdateReCall',
  /** 合同项目变更提交审批 */
  ContractUpdateSubmit: '/ContractUpdate/ContractUpdateSubmit',
  /** 删除合同变更 */
  DeleteContractUpdate: '/ContractUpdate/DeleteContractUpdate',
  /** 合同项目变更审批通过 */
  ExamineContractUpdate: '/ContractUpdate/ExamineContractUpdate',
  /** 获取合同变更列表 */
  GetContractUpdateList: '/ContractUpdate/GetContractUpdateList',
  /** 获取合同变更Count */
  GetContractUpdateListCount: '/ContractUpdate/GetContractUpdateListCount',
  /** 删除合同变更 */
  GetContractUpdateSingle: '/ContractUpdate/GetContractUpdateSingle',
  /** 是否作废合同项目变更 */
  IsCancelContractUpdate: '/ContractUpdate/IsCancelContractUpdate',
  /** 判断能否撤回 */
  ProReCallJudge: '/ContractUpdate/ProReCallJudge',
  /** 合同项目变更审批不通过 */
  UnExamineContractUpdate: '/ContractUpdate/UnExamineContractUpdate',
  /** 修改合同变更 */
  UpdateContractUpdate: '/ContractUpdate/UpdateContractUpdate',
}
export const apiAutoCoordinateMeeting = {
  /** 新增协调会议信息 */
  AddCoordinateMeeting: '/CoordinateMeeting/AddCoordinateMeeting',
  /** 作废协调会议信息 */
  CancelCoordinateMeeting: '/CoordinateMeeting/CancelCoordinateMeeting',
  /** 删除协调会议信息 */
  DeleteCoordinateMeeting: '/CoordinateMeeting/DeleteCoordinateMeeting',
  /** 获取协调会议信息 */
  GetCoordinateMeetingList: '/CoordinateMeeting/GetCoordinateMeetingList',
  /** 修改协调会议信息 */
  UpdateCoordinateMeeting: '/CoordinateMeeting/UpdateCoordinateMeeting',
}
export const apiAutoCostApply = {
  /** 新增用款申请 */
  AddCostApply: '/CostApply/AddCostApply',
  /** 回执 */
  CostApplyHz: '/CostApply/CostApplyHz',
  /** 支付完成 */
  CostApplyPaid: '/CostApply/CostApplyPaid',
  /** 删除用款申请 */
  DeleteCostApply: '/CostApply/DeleteCostApply',
  /** 审批通过 */
  ExamineCostApply: '/CostApply/ExamineCostApply',
  /** 用款申请状态统计 */
  GetCostApplyCount: '/CostApply/GetCostApplyCount',
  /** 导出用款申请 */
  GetCostApplyDetailExcel: '/CostApply/GetCostApplyDetailExcel',
  /** 查询用款申请 */
  GetCostApplyList: '/CostApply/GetCostApplyList',
  /** 查询用款申请单条 */
  GetCostApplySingle: '/CostApply/GetCostApplySingle',
  /** 提交审批 */
  SubmitExamineCostApply: '/CostApply/SubmitExamineCostApply',
  /** 审批不通过 */
  UnExamineCostApply: '/CostApply/UnExamineCostApply',
  /** 修改用款申请 */
  UpdateCostApply: '/CostApply/UpdateCostApply',
}
export const apiAutoCostIndex = {
  /** 新增造价指标 */
  AddCostIndex: '/CostIndex/AddCostIndex',
  /** 删除造价指标 */
  DeleteCostIndex: '/CostIndex/DeleteCostIndex',
  /** 获取造价指标列表 */
  GetCostIndexList: '/CostIndex/GetCostIndexList',
  /** 修改造价指标 */
  UpdateCostIndex: '/CostIndex/UpdateCostIndex',
  /** 批量修改造价指标 */
  UpdateCostIndexBath: '/CostIndex/UpdateCostIndexBath',
}
export const apiAutoCosttopproject = {
  /** 新增整体项目 */
  AddCosttopproject: '/Costtopproject/AddCosttopproject',
  /** 整体项目提交审批 */
  CosttopprojectSubmit: '/Costtopproject/CosttopprojectSubmit',
  /** 删除整体项目 */
  DeleteCosttopproject: '/Costtopproject/DeleteCosttopproject',
  /** 整体项目审批通过 */
  ExamineCosttopproject: '/Costtopproject/ExamineCosttopproject',
  /** 获取整体项目列表 */
  GetCosttopprojectList: '/Costtopproject/GetCosttopprojectList',
  /** 获取整体项目列表统计 */
  GetCosttopprojectListCount: '/Costtopproject/GetCosttopprojectListCount',
  /** 获取整体项目关联项目统计 */
  GetCosttopprojectReProjectCount: '/Costtopproject/GetCosttopprojectReProjectCount',
  /** 获取整体项目--单条 */
  GetCosttopprojectSingle: '/Costtopproject/GetCosttopprojectSingle',
  /** 作废-恢复整体项目 */
  IsCancelCosttopproject: '/Costtopproject/IsCancelCosttopproject',
  /** 整体项目审批不通过 */
  UnExamineCosttopproject: '/Costtopproject/UnExamineCosttopproject',
  /** 修改整体项目 */
  UpdateCosttopproject: '/Costtopproject/UpdateCosttopproject',
}
export const apiAutoCrOperationLog = {
  /** 获取编码规则操作日志 */
  GetCrOperationLogList: '/CrOperationLog/GetCrOperationLogList',
}
export const apiAutoCreateIncome = {
  /** 新增创收 */
  AddCreateIncome: '/CreateIncome/AddCreateIncome',
  /** 新增创收--新 */
  AddCreateIncomeNew: '/CreateIncome/AddCreateIncomeNew',
  /** 导入并插入数据库 */
  AddOutputValueDepWithPerByExcel: '/CreateIncome/AddOutputValueDepWithPerByExcel',
  /** 计算招标项目创收基本费 */
  CalcProBiddingAgencyCreateIncomeBaseFee: '/CreateIncome/CalcProBiddingAgencyCreateIncomeBaseFee',
  /** 删除创收 */
  DeleteCreateIncome: '/CreateIncome/DeleteCreateIncome',
  /** 审批通过创收单 */
  ExamineIncome: '/CreateIncome/ExamineIncome',
  /** 导出项目创收汇总 */
  ExportCreateIncomeHzList: '/CreateIncome/ExportCreateIncomeHzList',
  /** 导出项目创收汇总 */
  ExportCreateIncomeHzListNew: '/CreateIncome/ExportCreateIncomeHzListNew',
  /** 导出个人创收单 */
  ExportCreateIncomeList: '/CreateIncome/ExportCreateIncomeList',
  /** 导出个人创收单 */
  ExportCreateIncomeListNew: '/CreateIncome/ExportCreateIncomeListNew',
  /**  */
  ExportCreateIncomeListNewest: '/CreateIncome/ExportCreateIncomeListNewest',
  /** 导出个人财务统计 */
  ExportInvoiceTjList: '/CreateIncome/ExportInvoiceTjList',
  /** 导出开票记录 */
  ExportInvoiceTjListNew: '/CreateIncome/ExportInvoiceTjListNew',
  /** 部门产值表导出 */
  ExportOutputValueByDep: '/CreateIncome/ExportOutputValueByDep',
  /** 部门产值表记录简易导出 */
  ExportOutputValueByDepSimple: '/CreateIncome/ExportOutputValueByDepSimple',
  /** 部门人员产值表导出 */
  ExportOutputValueByPer: '/CreateIncome/ExportOutputValueByPer',
  /** 部门人员产值表记录简易导出 */
  ExportOutputValueByPerSimple: '/CreateIncome/ExportOutputValueByPerSimple',
  /** 导出自定义地址造价咨询统计 */
  ExportProCreateIncomeTjList: '/CreateIncome/ExportProCreateIncomeTjList',
  /** 导出综合统计员工开票 */
  ExportZhInvoiceTjList: '/CreateIncome/ExportZhInvoiceTjList',
  /** 人员部门保证金统计 */
  GetBidbondCountByPer: '/CreateIncome/GetBidbondCountByPer',
  /** 获取用章对应的创收单列表--带人员 部门 */
  GetCreateIncomeAndDisListByInvoice: '/CreateIncome/GetCreateIncomeAndDisListByInvoice',
  /** 创收   保证金   统计 */
  GetCreateIncomeCount: '/CreateIncome/GetCreateIncomeCount',
  /** 获取创收列表--待开票 */
  GetCreateIncomeDkpList: '/CreateIncome/GetCreateIncomeDkpList',
  /** 创收汇总列表页 */
  GetCreateIncomeHzList: '/CreateIncome/GetCreateIncomeHzList',
  /** 创收汇总列表页统计 */
  GetCreateIncomeHzListCount: '/CreateIncome/GetCreateIncomeHzListCount',
  /** 项目创收汇总统计 */
  GetCreateIncomeHzListCountSumMoney: '/CreateIncome/GetCreateIncomeHzListCountSumMoney',
  /** 获取创收列表 */
  GetCreateIncomeList: '/CreateIncome/GetCreateIncomeList',
  /** 获取用章对应的创收单列表 */
  GetCreateIncomeListByInvoice: '/CreateIncome/GetCreateIncomeListByInvoice',
  /** 导出 */
  GetCreateIncomeListExcel: '/CreateIncome/GetCreateIncomeListExcel',
  /** 获取创收---单条 */
  GetCreateIncomeSingle: '/CreateIncome/GetCreateIncomeSingle',
  /** 创收统计列表 */
  GetCreateIncomeTjList: '/CreateIncome/GetCreateIncomeTjList',
  /** 部门创收 */
  GetDepCreateIncomeList: '/CreateIncome/GetDepCreateIncomeList',
  /** 人员部门保证金统计---新 */
  GetDepositCountByPer: '/CreateIncome/GetDepositCountByPer',
  /** 企业台账——保证金统计 */
  GetDepositTzCount: '/CreateIncome/GetDepositTzCount',
  /** 开票回款统计  账号分组 财务 */
  GetHkCountMoneyByAccountBkTime: '/CreateIncome/GetHkCountMoneyByAccountBkTime',
  /** 开票回款统计  部门、账号分组  财务 */
  GetHkCountMoneyByDepAccountBkTime: '/CreateIncome/GetHkCountMoneyByDepAccountBkTime',
  /** 发票统计列表 */
  GetInvoiceTjList: '/CreateIncome/GetInvoiceTjList',
  /** 创收统计--按部门 */
  GetKpCountByDep: '/CreateIncome/GetKpCountByDep',
  /** 创收统计--按人员部门 */
  GetKpCountByPer: '/CreateIncome/GetKpCountByPer',
  /** 人员部门开票金额统计 */
  GetKpCountMoneyByPer: '/CreateIncome/GetKpCountMoneyByPer',
  /** 开票回款统计  账号分组 */
  GetKpHkCountMoneyByAccount: '/CreateIncome/GetKpHkCountMoneyByAccount',
  /** 开票回款统计  部门、账号分组 */
  GetKpHkCountMoneyByDepAccount: '/CreateIncome/GetKpHkCountMoneyByDepAccount',
  /** 从excel读取部门及人员 */
  GetOutputValueDepWithPerFromExcel: '/CreateIncome/GetOutputValueDepWithPerFromExcel',
  /** 我的创收 */
  GetOwnCreateIncomeList: '/CreateIncome/GetOwnCreateIncomeList',
  /** 人员部门项目数量统计 */
  GetProCountNumByPer: '/CreateIncome/GetProCountNumByPer',
  /** 项目创收--统计数字 */
  GetProCreateIncomeCount: '/CreateIncome/GetProCreateIncomeCount',
  /** 项目创收 */
  GetProCreateIncomeList: '/CreateIncome/GetProCreateIncomeList',
  /** 项目创收统计--对应金额统计 */
  GetProCreateIncomeTj: '/CreateIncome/GetProCreateIncomeTj',
  /** 项目创收统计 */
  GetProCreateIncomeTjList: '/CreateIncome/GetProCreateIncomeTjList',
  /** 创收--统计数字 */
  GetVCreateIncomeCount: '/CreateIncome/GetVCreateIncomeCount',
  /** 创收--统计金额 */
  GetVCreateIncomeSumMoney: '/CreateIncome/GetVCreateIncomeSumMoney',
  /** 人员部门创收金额统计 */
  GetcCiCounMoneyByPer: '/CreateIncome/GetcCiCounMoneyByPer',
  /** 是否作废创收单 */
  IsCancelCreateIncome: '/CreateIncome/IsCancelCreateIncome',
  /** 项目创收汇总统计 */
  JudgeOutputValuePerYear: '/CreateIncome/JudgeOutputValuePerYear',
  /** 部门产值统计 */
  OutputValueByDep: '/CreateIncome/OutputValueByDep',
  /** 人员产值统计 */
  OutputValueByPer: '/CreateIncome/OutputValueByPer',
  /** 部门产值汇总 */
  OutputValueBySktypeSummary: '/CreateIncome/OutputValueBySktypeSummary',
  /** 审批不通过收单 */
  UnExamineIncome: '/CreateIncome/UnExamineIncome',
  /** 修改创收 */
  UpdateCreateIncome: '/CreateIncome/UpdateCreateIncome',
  /** 修改产值核对再分配字段 */
  UpdateCreateIncomeHzIsDis: '/CreateIncome/UpdateCreateIncomeHzIsDis',
  /** 修改创收--新 */
  UpdateCreateIncomeNew: '/CreateIncome/UpdateCreateIncomeNew',
}
export const apiAutoCreateIncomeRedistribution = {
  /** 新增创收单再分配 */
  AddCreateIncomeRedistribution: '/CreateIncomeRedistribution/AddCreateIncomeRedistribution',
  /** 统计创收 */
  CreateIncomeRedistributionCount: '/CreateIncomeRedistribution/CreateIncomeRedistributionCount',
  /** 删除创收单再分配 */
  DeleteCreateIncomeRedistribution: '/CreateIncomeRedistribution/DeleteCreateIncomeRedistribution',
  /** 审批通过创收单再分配 */
  ExamineIncome: '/CreateIncomeRedistribution/ExamineIncome',
  /** 获取创收单再分配列表 */
  GetCreateIncomeRedistributionList: '/CreateIncomeRedistribution/GetCreateIncomeRedistributionList',
  /** 获取创收单再分配--单条 */
  GetCreateIncomeRedistributionSingle: '/CreateIncomeRedistribution/GetCreateIncomeRedistributionSingle',
  /** 同步创收再分配 */
  TbIncomeZfp: '/CreateIncomeRedistribution/TbIncomeZfp',
  /** 审批不通过创收单再分配 */
  UnExamineIncome: '/CreateIncomeRedistribution/UnExamineIncome',
  /** 修改创收单再分配 */
  UpdateCreateIncomeRedistribution: '/CreateIncomeRedistribution/UpdateCreateIncomeRedistribution',
}
export const apiAutoCtOperationLog = {
  /** 获取合同操作日志 */
  GetCtOperationLogList: '/CtOperationLog/GetCtOperationLogList',
}
export const apiAutoCustomAddress = {
  /** 新增自定义地址 */
  AddCustomAddress: '/CustomAddress/AddCustomAddress',
  /** 删除自定义地址 */
  DeleteCustomAddress: '/CustomAddress/DeleteCustomAddress',
  /** 获取自定义地址列表 */
  GetCustomAddressList: '/CustomAddress/GetCustomAddressList',
  /** 修改自定义地址 */
  UpdateCustomAddress: '/CustomAddress/UpdateCustomAddress',
}
export const apiAutoCustomJob = {
  /** 新增自定义作业 */
  AddCustomJob: '/CustomJob/AddCustomJob',
  /** 自定义作业提交审批 */
  CustomJobSubmit: '/CustomJob/CustomJobSubmit',
  /** 删除自定义作业 */
  DeleteCustomJob: '/CustomJob/DeleteCustomJob',
  /** 自定义作业审批通过 */
  ExamineCustomJob: '/CustomJob/ExamineCustomJob',
  /** 获取自定义作业Count */
  GetCustomJobCount: '/CustomJob/GetCustomJobCount',
  /** 获取自定义作业列表 */
  GetCustomJobList: '/CustomJob/GetCustomJobList',
  /** 获取自定义作业单条 */
  GetCustomJobSingle: '/CustomJob/GetCustomJobSingle',
  /** 自定义作业审批不通过 */
  UnExamineCustomJob: '/CustomJob/UnExamineCustomJob',
  /** 修改自定义作业 */
  UpdateCustomJob: '/CustomJob/UpdateCustomJob',
}
export const apiAutoCustomerInfo = {
  /** 新增客户信息 */
  AddCustomerInfo: '/CustomerInfo/AddCustomerInfo',
  /** 新增客户信息以及联系人 */
  AddCustomerInfoAndUser: '/CustomerInfo/AddCustomerInfoAndUser',
  /** 新增客户联系人 */
  AddCustomerUser: '/CustomerInfo/AddCustomerUser',
  /** 作废或者取消作废客户信息 */
  CancelOrNotCustomerInfo: '/CustomerInfo/CancelOrNotCustomerInfo',
  /** 删除客户信息 */
  DeleteCustomerInfo: '/CustomerInfo/DeleteCustomerInfo',
  /** 删除客户联系人 */
  DeleteCustomerUser: '/CustomerInfo/DeleteCustomerUser',
  /** 导出自定义地址客户统计 */
  ExportCusAddressCustomerinfo: '/CustomerInfo/ExportCusAddressCustomerinfo',
  /** 导出客户信息 */
  ExportCustomerinfo: '/CustomerInfo/ExportCustomerinfo',
  /** 获取客户信息列表 */
  GetCustomerInfoList: '/CustomerInfo/GetCustomerInfoList',
  /** 统计客户信息列表 */
  GetCustomerInfoListCount: '/CustomerInfo/GetCustomerInfoListCount',
  /** 统计客户信息列表综合统计 */
  GetCustomerInfoListSynthesizeCount: '/CustomerInfo/GetCustomerInfoListSynthesizeCount',
  /** 统计数量 */
  GetCustomerInfoNumCount: '/CustomerInfo/GetCustomerInfoNumCount',
  /** 获取客户信息单条 */
  GetCustomerInfoSingle: '/CustomerInfo/GetCustomerInfoSingle',
  /** 获取客户联系人列表 */
  GetCustomerUserList: '/CustomerInfo/GetCustomerUserList',
  /** 获取客户联系人--单条 */
  GetCustomerUserSingle: '/CustomerInfo/GetCustomerUserSingle',
  /** 修改客户信息 */
  UpdateCustomerInfo: '/CustomerInfo/UpdateCustomerInfo',
  /** 修改客户联系人 */
  UpdateCustomerUser: '/CustomerInfo/UpdateCustomerUser',
  /** 客户信息导入 */
  UploadCustomerInfoWithPer: '/CustomerInfo/UploadCustomerInfoWithPer',
}
export const apiAutoDailyConfig = {
  /** 新增或修改日报配置 */
  AddOrUpdateDailyConfig: '/DailyConfig/AddOrUpdateDailyConfig',
  /** 删除日报配置 */
  DeleteDailyConfig: '/DailyConfig/DeleteDailyConfig',
  /** 获取日报配置列表 */
  GetDailyConfigList: '/DailyConfig/GetDailyConfigList',
  /** 获取日报配置单条 */
  GetDailyConfigSingle: '/DailyConfig/GetDailyConfigSingle',
}
export const apiAutoDailyManage = {
  /** 新增日报管理 */
  AddDailyManage: '/DailyManage/AddDailyManage',
  /** 评分 */
  DailyManagePf: '/DailyManage/DailyManagePf',
  /**  */
  DealDailyManage: '/DailyManage/DealDailyManage',
  /** 删除日报管理 */
  DeleteDailyManage: '/DailyManage/DeleteDailyManage',
  /** 全过程日报审批通过 */
  ExamineDailyManage: '/DailyManage/ExamineDailyManage',
  /** 导出日报总汇 */
  ExportDailyManage: '/DailyManage/ExportDailyManage',
  /** 获取日报管理列表 */
  GetDailyManageCountNew: '/DailyManage/GetDailyManageCountNew',
  /** 获取日报管理列表 */
  GetDailyManageList: '/DailyManage/GetDailyManageList',
  /** 统计日报管理列表 */
  GetDailyManageListCount: '/DailyManage/GetDailyManageListCount',
  /** 获取日报管理单条 */
  GetDailyManageSingle: '/DailyManage/GetDailyManageSingle',
  /** 用户当天是否填写 */
  IsDailyManage: '/DailyManage/IsDailyManage',
  /** 提交日报管理 */
  SubmitDailyManage: '/DailyManage/SubmitDailyManage',
  /** 通过或打回 */
  TgOrDhDailyManage: '/DailyManage/TgOrDhDailyManage',
  /** 全过程日报审批不通过 */
  UnExamineDailyManage: '/DailyManage/UnExamineDailyManage',
  /** 修改日报管理 */
  UpdateDailyManage: '/DailyManage/UpdateDailyManage',
}
export const apiAutoDailyPer = {
  /** 新增日报填写人员配置 */
  AddDailyPer: '/DailyPer/AddDailyPer',
  /** 删除日报填写人员配置 */
  DeleteDailyPer: '/DailyPer/DeleteDailyPer',
  /** 获取日报填写人员配置列表 */
  GetDailyPerList: '/DailyPer/GetDailyPerList',
  /** 获取日报填写人员配置--当前用户 */
  GetDailyPerSingle: '/DailyPer/GetDailyPerSingle',
  /** 修改日报填写人员配置 */
  UpdateDailyPer: '/DailyPer/UpdateDailyPer',
}
export const apiAutoDailyType = {
  /** 新增日报类型 */
  AddDailyType: '/DailyType/AddDailyType',
  /** 删除日报类型 */
  DeleteDailyType: '/DailyType/DeleteDailyType',
  /** 获取日报类型列表 */
  GetDailyTypeList: '/DailyType/GetDailyTypeList',
  /** 修改日报类型 */
  UpdateDailyType: '/DailyType/UpdateDailyType',
}
export const apiAutoDepBelongConfig = {
  /** 新增部门归属全局配置 */
  AddDepBelongConfig: '/DepBelongConfig/AddDepBelongConfig',
  /** 删除部门归属全局配置 */
  DeleteDepBelongConfig: '/DepBelongConfig/DeleteDepBelongConfig',
  /** 获取部门归属全局配置列表 */
  GetDepBelongConfigList: '/DepBelongConfig/GetDepBelongConfigList',
  /** 修改部门归属全局配置 */
  UpdateDepBelongConfig: '/DepBelongConfig/UpdateDepBelongConfig',
}
export const apiAutoDepNetworkDisk = {
  /** 新增部门网盘 */
  AddDepNetworkDisk: '/DepNetworkDisk/AddDepNetworkDisk',
  /** 删除部门网盘 */
  DeleteDepNetworkDisk: '/DepNetworkDisk/DeleteDepNetworkDisk',
  /** 获取部门网盘列表 */
  GetDepNetworkDiskList: '/DepNetworkDisk/GetDepNetworkDiskList',
  /** 获取部门网盘--单条 */
  GetDepNetworkDiskSingle: '/DepNetworkDisk/GetDepNetworkDiskSingle',
  /** 修改部门网盘 */
  UpdateDepNetworkDisk: '/DepNetworkDisk/UpdateDepNetworkDisk',
}
export const apiAutoDeposit = {
  /** 新增保证金 */
  AddDeposit: '/Deposit/AddDeposit',
  /** 作废保证金 */
  CancelDeposit: '/Deposit/CancelDeposit',
  /** 删除保证金 */
  DeleteDeposit: '/Deposit/DeleteDeposit',
  /** 保证金提交审批 */
  DepositSubmit: '/Deposit/DepositSubmit',
  /** 保证金审批通过 */
  ExamineDeposit: '/Deposit/ExamineDeposit',
  /** 导出自定义地址保证金统计 */
  ExportCusAddressDeposit: '/Deposit/ExportCusAddressDeposit',
  /** 导出保证金记录 */
  ExportDeposit: '/Deposit/ExportDeposit',
  /** 保证金统计 */
  GetDepositCount: '/Deposit/GetDepositCount',
  /** 保证金统计金额 */
  GetDepositCountMoney: '/Deposit/GetDepositCountMoney',
  /** 获取保证金列表 */
  GetDepositList: '/Deposit/GetDepositList',
  /** 获取保证金---单条 */
  GetDepositSingle: '/Deposit/GetDepositSingle',
  /** 保证金审批不通过 */
  UnExamineDeposit: '/Deposit/UnExamineDeposit',
  /** 修改保证金 */
  UpdateDeposit: '/Deposit/UpdateDeposit',
}
export const apiAutoDepositBack = {
  /** 新增保证金回款 */
  AddDepositBack: '/DepositBack/AddDepositBack',
  /** 处理实际回款时间 */
  BtDepositBackTime: '/DepositBack/BtDepositBackTime',
  /** 删除保证金回款 */
  DeleteDepositBack: '/DepositBack/DeleteDepositBack',
  /** 导出保证金回款记录 */
  ExportDepositBack: '/DepositBack/ExportDepositBack',
  /** 获取保证金回款列表 */
  GetDepositBackList: '/DepositBack/GetDepositBackList',
  /** 保证金回款--金额 */
  GetDepositBackSumMoney: '/DepositBack/GetDepositBackSumMoney',
  /** 是否作废保证金回款 */
  IsCancelDepositBack: '/DepositBack/IsCancelDepositBack',
  /** 修改保证金回款 */
  UpdateDepositBack: '/DepositBack/UpdateDepositBack',
}
export const apiAutoDepositPay = {
  /** 新增保证金支付 */
  AddDepositPay: '/DepositPay/AddDepositPay',
  /** 删除保证金支付 */
  DeleteDepositPay: '/DepositPay/DeleteDepositPay',
  /** 获取保证金支付列表 */
  GetDepositPayList: '/DepositPay/GetDepositPayList',
  /** 保证金支付--金额 */
  GetDepositPaySumMoney: '/DepositPay/GetDepositPaySumMoney',
  /** 是否作废保证金支付 */
  IsCancelDepositPay: '/DepositPay/IsCancelDepositPay',
  /** 修改保证金支付 */
  UpdateDepositPay: '/DepositPay/UpdateDepositPay',
}
export const apiAutoDepositReturnUpdate = {
  /** 新增保证金退还负责人变更 */
  AddDepositReturnUpdate: '/DepositReturnUpdate/AddDepositReturnUpdate',
  /** 作废保证金退还负责人变更 */
  CancelDepositReturnUpdate: '/DepositReturnUpdate/CancelDepositReturnUpdate',
  /** 删除保证金退还负责人变更 */
  DeleteDepositReturnUpdate: '/DepositReturnUpdate/DeleteDepositReturnUpdate',
  /** 保证金退还负责人变更提交审批 */
  DepositReturnUpdateSubmit: '/DepositReturnUpdate/DepositReturnUpdateSubmit',
  /** 保证金退还负责人变更审批通过 */
  ExamineDepositReturnUpdate: '/DepositReturnUpdate/ExamineDepositReturnUpdate',
  /** 获取保证金退还负责人变更列表 */
  GetDepositReturnUpdateList: '/DepositReturnUpdate/GetDepositReturnUpdateList',
  /** 获取保证金退还负责人变更---单条 */
  GetDepositReturnUpdateSingle: '/DepositReturnUpdate/GetDepositReturnUpdateSingle',
  /** 保证金退还负责人变更审批不通过 */
  UnExamineDepositReturnUpdate: '/DepositReturnUpdate/UnExamineDepositReturnUpdate',
  /** 修改保证金退还负责人变更 */
  UpdateDepositReturnUpdate: '/DepositReturnUpdate/UpdateDepositReturnUpdate',
}
export const apiAutoDictionary = {
  /** 新增字典 */
  AddDictionary: '/Dictionary/AddDictionary',
  /** 新增字典类型 */
  AddDictionaryType: '/Dictionary/AddDictionaryType',
  /** 删除字典 */
  DeleteDictionary: '/Dictionary/DeleteDictionary',
  /** 删除字典类型 */
  DeleteDictionaryType: '/Dictionary/DeleteDictionaryType',
  /** 获取字典列表 */
  GetDictionaryList: '/Dictionary/GetDictionaryList',
  /** 获取字典类型列表 */
  GetDictionaryType: '/Dictionary/GetDictionaryType',
  /** 修改字典 */
  UpdateDictionary: '/Dictionary/UpdateDictionary',
  /** 修改字典类型 */
  UpdateDictionaryType: '/Dictionary/UpdateDictionaryType',
}
export const apiAutoDingding = {
  /**  */
  RedirectUrl: '/Dingding/RedirectUrl',
}
export const apiAutoDocumentTemplate = {
  /** 新增文档模板 */
  AddDocumentTemplate: '/DocumentTemplate/AddDocumentTemplate',
  /** 批量新增文档模板 */
  BathAddDocumentTemplate: '/DocumentTemplate/BathAddDocumentTemplate',
  /** 删除文档模板 */
  DeleteDocumentTemplate: '/DocumentTemplate/DeleteDocumentTemplate',
  /** 获取文档模板列表 */
  GetDocumentTemplateList: '/DocumentTemplate/GetDocumentTemplateList',
  /** 修改文档模板 */
  UpdateDocumentTemplate: '/DocumentTemplate/UpdateDocumentTemplate',
  /** 修改排序 */
  UpdateDocumentTemplateSort: '/DocumentTemplate/UpdateDocumentTemplateSort',
}
export const apiAutoDriver = {
  /** 新增驾驶员 */
  AddDriver: '/Driver/AddDriver',
  /** 删除驾驶员 */
  DeleteDriver: '/Driver/DeleteDriver',
  /** 获取驾驶员列表 */
  GetDriverList: '/Driver/GetDriverList',
  /** 获取驾驶员列表 */
  GetDriverListNew: '/Driver/GetDriverListNew',
  /** 获取驾驶员单条 */
  GetDriverSingle: '/Driver/GetDriverSingle',
  /** 修改驾驶员 */
  UpdateDriver: '/Driver/UpdateDriver',
}
export const apiAutoElectronicReceipt = {
  /** 新增电子收据 */
  AddElectronicReceipt: '/ElectronicReceipt/AddElectronicReceipt',
  /** 删除电子收据 */
  DeleteElectronicReceipt: '/ElectronicReceipt/DeleteElectronicReceipt',
  /** 获取电子收据列表 */
  GetElectronicReceiptList: '/ElectronicReceipt/GetElectronicReceiptList',
  /** 获取电子收据Count */
  GetElectronicReceiptListCount: '/ElectronicReceipt/GetElectronicReceiptListCount',
  /** 获取电子收据单条 */
  GetElectronicReceiptSingle: '/ElectronicReceipt/GetElectronicReceiptSingle',
  /** 是否作废电子收据 */
  IsCancelElectronicReceipt: '/ElectronicReceipt/IsCancelElectronicReceipt',
  /** 修改电子收据 */
  UpdateElectronicReceipt: '/ElectronicReceipt/UpdateElectronicReceipt',
  /** 修改收据生成次数 */
  UpdateElectronicReceiptCreateNum: '/ElectronicReceipt/UpdateElectronicReceiptCreateNum',
  /** 修改电子收据--报告号 */
  UpdateElectronicReceiptNo: '/ElectronicReceipt/UpdateElectronicReceiptNo',
}
export const apiAutoEnterpriseLedger = {
  /** 导出业务台账 */
  ExportVBusinessLedgerList: '/EnterpriseLedger/ExportVBusinessLedgerList',
  /** 导出合同台账 */
  ExportVContractLedgerList: '/EnterpriseLedger/ExportVContractLedgerList',
  /** 导出保证金台账 */
  ExportVDepositLedgerList: '/EnterpriseLedger/ExportVDepositLedgerList',
  /** 导出招标代理台账汇总 */
  ExportVProBiddingAgencyList: '/EnterpriseLedger/ExportVProBiddingAgencyList',
  /** 导出造价咨询台账 */
  ExportVProjectInfoList: '/EnterpriseLedger/ExportVProjectInfoList',
  /** 导出全过程咨询台账汇总 */
  ExportVQgczxProjectList: '/EnterpriseLedger/ExportVQgczxProjectList',
  /** 发票台账统计金额-v2 */
  GetInvoiceLedgerCountMoneyV2: '/EnterpriseLedger/GetInvoiceLedgerCountMoneyV2',
  /** 发票台账-v2(Excel导出) */
  GetInvoiceLedgerExcelV2: '/EnterpriseLedger/GetInvoiceLedgerExcelV2',
  /** 发票台账-v2 */
  GetInvoiceLedgerListV2: '/EnterpriseLedger/GetInvoiceLedgerListV2',
  /** 业务台账汇总统计 */
  GetVBusinessLedgerCountMoney: '/EnterpriseLedger/GetVBusinessLedgerCountMoney',
  /** 业务台账 */
  GetVBusinessLedgerList: '/EnterpriseLedger/GetVBusinessLedgerList',
  /** 合同台账汇总统计 */
  GetVContractLedgerCountMoney: '/EnterpriseLedger/GetVContractLedgerCountMoney',
  /** 合同台账 */
  GetVContractLedgerList: '/EnterpriseLedger/GetVContractLedgerList',
  /** 保证金台账 */
  GetVDepositLedgerList: '/EnterpriseLedger/GetVDepositLedgerList',
  /** 招标代理台账 */
  GetVProBiddingAgencyList: '/EnterpriseLedger/GetVProBiddingAgencyList',
  /** 招标代理台账汇总统计 */
  GetVProBiddingAgencyListCount: '/EnterpriseLedger/GetVProBiddingAgencyListCount',
  /** 导出招标代理台账汇总 */
  GetVProBiddingAgencyListExcel: '/EnterpriseLedger/GetVProBiddingAgencyListExcel',
  /** 获取人员招标代理台账费用统计 */
  GetVProBiddingAgencyUserList: '/EnterpriseLedger/GetVProBiddingAgencyUserList',
  /** 造价咨询台账 */
  GetVProjectInfoList: '/EnterpriseLedger/GetVProjectInfoList',
  /** 造价咨询台账汇总统计 */
  GetVProjectInfoListCount: '/EnterpriseLedger/GetVProjectInfoListCount',
  /** 导出造价咨询台账 */
  GetVProjectInfoListExcel: '/EnterpriseLedger/GetVProjectInfoListExcel',
  /** 获取部门造价咨询台账费用统计 */
  GetVProjectInfoUserDepList: '/EnterpriseLedger/GetVProjectInfoUserDepList',
  /** 获取人员造价咨询台账费用统计 */
  GetVProjectInfoUserList: '/EnterpriseLedger/GetVProjectInfoUserList',
  /** 全过程咨询台账 */
  GetVQgczxProjectList: '/EnterpriseLedger/GetVQgczxProjectList',
  /** 全过程咨询台账汇总统计 */
  GetVQgczxProjectListCount: '/EnterpriseLedger/GetVQgczxProjectListCount',
  /** 导出全过程咨询台账汇总 */
  GetVQgczxProjectListExcel: '/EnterpriseLedger/GetVQgczxProjectListExcel',
  /** 获取部门全过程咨询台账费用统计 */
  GetVQgczxProjectUserDepList: '/EnterpriseLedger/GetVQgczxProjectUserDepList',
  /** 获取人员全过程咨询台账费用统计 */
  GetVQgczxProjectUserList: '/EnterpriseLedger/GetVQgczxProjectUserList',
  /** 获取部门招标代理台账费用统计 */
  GetZbdlDepSumMoneyList: '/EnterpriseLedger/GetZbdlDepSumMoneyList',
}
export const apiAutoEnterpriseLedgerDashboard = {
  /** 合同管理 */
  GetEnterpriseLedgerDashboardContractCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardContractCount',
  /** 保证金管理 */
  GetEnterpriseLedgerDashboardDepositCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardDepositCount',
  /** 开票管理 */
  GetEnterpriseLedgerDashboardInvoiceCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardInvoiceCount',
  /** 招标代理 */
  GetEnterpriseLedgerDashboardProBiddingAgencyCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardProBiddingAgencyCount',
  /** 招标代理 */
  GetEnterpriseLedgerDashboardProBiddingOperationCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardProBiddingOperationCount',
  /** 造价咨询-计数 */
  GetEnterpriseLedgerDashboardProjectInfoCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardProjectInfoCount',
  /** 全过程咨询 */
  GetEnterpriseLedgerDashboardQgczxProjectCount: '/EnterpriseLedgerDashboard/GetEnterpriseLedgerDashboardQgczxProjectCount',
}
export const apiAutoExExam = {
  /** 添加考试 */
  AddExam: '/ExExam/AddExam',
  /** 生成试卷----自动覆盖 */
  AddExamPage: '/ExExam/AddExamPage',
  /** 添加考试记录 */
  AddExamRecord: '/ExExam/AddExamRecord',
  /** 添加临时考试记录 */
  AddExamTem: '/ExExam/AddExamTem',
  /** 提交答案---计算得分 */
  AddMeExamQuestions: '/ExExam/AddMeExamQuestions',
  /** 开始考试---添加 */
  AddMeExamRecord: '/ExExam/AddMeExamRecord',
  /** 自动交卷 */
  AutoAddMeExam: '/ExExam/AutoAddMeExam',
  /** 删除考试 */
  DelExam: '/ExExam/DelExam',
  /** 导出考试记录 */
  ExportExamrecord: '/ExExam/ExportExamrecord',
  /** 导出缺考人员记录 */
  ExportQkPerson: '/ExExam/ExportQkPerson',
  /** 获取缺考人员信息 */
  GetAppointAdminListInAdmin: '/ExExam/GetAppointAdminListInAdmin',
  /** 获取考试指定部门信息 */
  GetAppointOrganizationListInAdmin: '/ExExam/GetAppointOrganizationListInAdmin',
  /** 统计各部门人员的参考人数 */
  GetExamAvgNumListInAdmin: '/ExExam/GetExamAvgNumListInAdmin',
  /** 统计各部门人员的参考人数 */
  GetExamDpPerNumListInAdmin: '/ExExam/GetExamDpPerNumListInAdmin',
  /** 查询考试列表 */
  GetExamListInAdmin: '/ExExam/GetExamListInAdmin',
  /** 查询考试列表 */
  GetExamListOwnInAdmin: '/ExExam/GetExamListOwnInAdmin',
  /** 查看试卷 */
  GetExamPageListInAdmin: '/ExExam/GetExamPageListInAdmin',
  /** 查看试卷---新 */
  GetExamPageListInAdminNew: '/ExExam/GetExamPageListInAdminNew',
  /** 我的试卷 */
  GetExamPageListOwnInAdmin: '/ExExam/GetExamPageListOwnInAdmin',
  /** 我的试卷Count */
  GetExamPageListOwnInAdminCount: '/ExExam/GetExamPageListOwnInAdminCount',
  /** 查看试题 */
  GetExamQuestionsListInAdmin: '/ExExam/GetExamQuestionsListInAdmin',
  /** 我的试题 */
  GetExamQuestionsListOwnInAdmin: '/ExExam/GetExamQuestionsListOwnInAdmin',
  /** 查询临时考试记录列表 */
  GetExamTemListInAdmin: '/ExExam/GetExamTemListInAdmin',
  /** 答题记录 */
  GetMeExamquestionsListInAdmin: '/ExExam/GetMeExamquestionsListInAdmin',
  /** 我的答题记录 */
  GetMeExamquestionsListOwnInAdmin: '/ExExam/GetMeExamquestionsListOwnInAdmin',
  /** 考试记录 */
  GetMeExamrecordListInAdmin: '/ExExam/GetMeExamrecordListInAdmin',
  /** 我的考试记录 */
  GetMeExamrecordListOwnInAdmin: '/ExExam/GetMeExamrecordListOwnInAdmin',
  /** 我的考试记录Count */
  GetMeExamrecordListOwnInAdminCount: '/ExExam/GetMeExamrecordListOwnInAdminCount',
  /** 查询题目专业列表BYECDOE */
  GetQuestionsSpecialtyListByECodeInAdmin: '/ExExam/GetQuestionsSpecialtyListByECodeInAdmin',
  /** 查询单条考试 */
  GetSingleExamInAdmin: '/ExExam/GetSingleExamInAdmin',
  /** 查询单条考试 */
  GetSingleExamOwnInAdmin: '/ExExam/GetSingleExamOwnInAdmin',
  /** 获取单条考试 */
  GetSingleExamrecordOwnInAdmin: '/ExExam/GetSingleExamrecordOwnInAdmin',
  /** 导入题目 */
  ImprotQuestionWebModel: '/ExExam/ImprotQuestionWebModel',
  /** 修改考试 */
  UpdateExam: '/ExExam/UpdateExam',
}
export const apiAutoExMePractice = {
  /** 添加练习 */
  AddMePractice: '/ExMePractice/AddMePractice',
  /** 删除练习 */
  DelMePracticeTem: '/ExMePractice/DelMePracticeTem',
  /** 删除错题 */
  DelWrong: '/ExMePractice/DelWrong',
  /** 导出练习记录 */
  ExportMePractice: '/ExMePractice/ExportMePractice',
  /** 查询练习列表 */
  GetMePracticeListInAdmin: '/ExMePractice/GetMePracticeListInAdmin',
  /** 查询练习列表---自己 */
  GetMePracticeListOwnInAdmin: '/ExMePractice/GetMePracticeListOwnInAdmin',
  /** 查询练习记录列表 */
  GetMePracticeTemListInAdmin: '/ExMePractice/GetMePracticeTemListInAdmin',
  /** 查询错题列表 */
  GetMeWrongListInAdmin: '/ExMePractice/GetMeWrongListInAdmin',
  /** 查询错题列表---自己 */
  GetMeWrongListOwnInAdmin: '/ExMePractice/GetMeWrongListOwnInAdmin',
}
export const apiAutoExQuestions = {
  /** 添加题目 */
  AddQuestions: '/ExQuestions/AddQuestions',
  /** 删除题目 */
  DelQuestions: '/ExQuestions/DelQuestions',
  /** 导出题目 */
  ExportQuestions: '/ExQuestions/ExportQuestions',
  /** 统计题目数量 */
  GetQuestionNumListInAdmin: '/ExQuestions/GetQuestionNumListInAdmin',
  /** 查询题目列表 */
  GetQuestionsListInAdmin: '/ExQuestions/GetQuestionsListInAdmin',
  /** 随机获取100条题目 */
  GetQuestionsListRandomInAdmin: '/ExQuestions/GetQuestionsListRandomInAdmin',
  /** 获取单条题目 */
  GetSingleQuestionsInAdmin: '/ExQuestions/GetSingleQuestionsInAdmin',
  /** 修改题目 */
  UpdateQuestions: '/ExQuestions/UpdateQuestions',
}
export const apiAutoExQuestionsSpecialty = {
  /** 添加题目专业 */
  AddQuestionsSpecialty: '/ExQuestionsSpecialty/AddQuestionsSpecialty',
  /** 删除题目专业 */
  DelQuestionsSpecialty: '/ExQuestionsSpecialty/DelQuestionsSpecialty',
  /** 查询题目专业列表 */
  GetQuestionsSpecialtyListInAdmin: '/ExQuestionsSpecialty/GetQuestionsSpecialtyListInAdmin',
  /** 获取单条题目专业 */
  GetSingleQuestionsSpecialtyInAdmin: '/ExQuestionsSpecialty/GetSingleQuestionsSpecialtyInAdmin',
  /** 修改题目专业 */
  UpdateQuestionsSpecialty: '/ExQuestionsSpecialty/UpdateQuestionsSpecialty',
}
export const apiAutoExReferenceBook = {
  /** 添加参考书 */
  AddReferenceBook: '/ExReferenceBook/AddReferenceBook',
  /** 删除参考书 */
  DelReferenceBook: '/ExReferenceBook/DelReferenceBook',
  /** 查询参考书列表 */
  GetReferenceBookListInAdmin: '/ExReferenceBook/GetReferenceBookListInAdmin',
  /** 获取单条参考书 */
  GetSingleReferenceBookInAdmin: '/ExReferenceBook/GetSingleReferenceBookInAdmin',
  /** 修改参考书 */
  UpdateReferenceBook: '/ExReferenceBook/UpdateReferenceBook',
}
export const apiAutoExamine = {
  /** 新增业务流配置 */
  AddBusinessConfig: '/Examine/AddBusinessConfig',
  /** 新增审批配置--串行 */
  AddExamineConfig: '/Examine/AddExamineConfig',
  /** 并行节点新增---并行或条件(非首个) */
  AddExnodeConfigBxAdmin: '/Examine/AddExnodeConfigBxAdmin',
  /** 首次创建并行节点---并行或条件(首个) */
  CreateExnodeConfigBxAdmin: '/Examine/CreateExnodeConfigBxAdmin',
  /** 删除业务流配置 */
  DeleteBusinessConfig: '/Examine/DeleteBusinessConfig',
  /** 删除审批配置 */
  DeleteExamineConfig: '/Examine/DeleteExamineConfig',
  /** 获取业务流配置列表 */
  GeBusinessConfigList: '/Examine/GeBusinessConfigList',
  /** 获取业务配置列表统计 */
  GeBusinessConfigListCount: '/Examine/GeBusinessConfigListCount',
  /** 获取审批配置列表 */
  GetExamineConfigList: '/Examine/GetExamineConfigList',
  /** 获取审批历史列表 */
  GetExamineHistoryList: '/Examine/GetExamineHistoryList',
  /** 统一审批 */
  GetExamineList: '/Examine/GetExamineList',
  /** 获取当前用户，指定审批流、指定关联id可操作的节点 */
  GetExamineNodeByToken: '/Examine/GetExamineNodeByToken',
  /** 获取审批节点当前人  姓名 */
  GetExamineNodePernameList: '/Examine/GetExamineNodePernameList',
  /** 统一审批--单条 */
  GetExamineSingle: '/Examine/GetExamineSingle',
  /** 查询统一审批记录--我申请的 */
  GetOwnApplyExamineList: '/Examine/GetOwnApplyExamineList',
  /** 是否作废-恢复业务配置 */
  IsCancelBusinessConfig: '/Examine/IsCancelBusinessConfig',
  /** 修改业务流配置 */
  UpdateBusinessConfig: '/Examine/UpdateBusinessConfig',
  /** 修改并行审批配置---与  或 */
  UpdateExamineAndOrConfig: '/Examine/UpdateExamineAndOrConfig',
  /** 修改审批配置 */
  UpdateExamineConfig: '/Examine/UpdateExamineConfig',
}
export const apiAutoExpertRegistration = {
  /** 打回外部专家报名 */
  BackWarehouseExpertRegistration: '/ExpertRegistration/BackWarehouseExpertRegistration',
  /** 更新外部专家报名 */
  CreateExpertRegistration: '/ExpertRegistration/CreateExpertRegistration',
  /**  */
  DealExpertRegistrationData: '/ExpertRegistration/DealExpertRegistrationData',
  /** 删除外部专家报名 */
  DeleteExpertRegistration: '/ExpertRegistration/DeleteExpertRegistration',
  /** 入库外部专家报名 */
  EnWarehouseExpertRegistration: '/ExpertRegistration/EnWarehouseExpertRegistration',
  /** 出库外部专家报名 */
  ExWarehouseExpertRegistration: '/ExpertRegistration/ExWarehouseExpertRegistration',
  /** 获取外部专家报名配置（系统外） */
  GetExpertRegistrationConfig: '/ExpertRegistration/GetExpertRegistrationConfig',
  /** 获取外部专家报名配置（系统内） */
  GetExpertRegistrationConfigSingle: '/ExpertRegistration/GetExpertRegistrationConfigSingle',
  /** 获取外部专家报名计数 */
  GetExpertRegistrationCount: '/ExpertRegistration/GetExpertRegistrationCount',
  /** 获取外部专家报名列表 */
  GetExpertRegistrationList: '/ExpertRegistration/GetExpertRegistrationList',
  /** 导出 */
  GetExpertRegistrationListExcel: '/ExpertRegistration/GetExpertRegistrationListExcel',
  /** 获取外部专家报名列表 */
  GetExpertRegistrationSingle: '/ExpertRegistration/GetExpertRegistrationSingle',
  /** 更新外部专家报名 */
  UpdateExpertRegistration: '/ExpertRegistration/UpdateExpertRegistration',
  /** 更新外部专家报名配置 */
  UpdateExpertRegistrationConfig: '/ExpertRegistration/UpdateExpertRegistrationConfig',
}
export const apiAutoExportAdvisoryOpinion = {
  /** 咨询意见模板--导出 */
  ExportAdvisoryOpinionFile: '/ExportAdvisoryOpinion/ExportAdvisoryOpinionFile',
}
export const apiAutoExportZkPriceListByEs = {
  /** 导出信息价 */
  ExportZkPriceListByEs: '/ExportZkPriceListByEs/ExportZkPriceListByEs',
}
export const apiAutoExternalExpert = {
  /** 新增外部专业库 */
  AddExternalExpert: '/ExternalExpert/AddExternalExpert',
  /** 删除外部专业库 */
  DeleteExternalExpert: '/ExternalExpert/DeleteExternalExpert',
  /** 获取外部专业库列表 */
  GetExternalExpertList: '/ExternalExpert/GetExternalExpertList',
  /** 修改外部专业库 */
  UpdateExternalExpert: '/ExternalExpert/UpdateExternalExpert',
}
export const apiAutoExternalInspectionRecord = {
  /** 新增沟通记录外部检查记录 */
  AddExternalInspectionRecord: '/ExternalInspectionRecord/AddExternalInspectionRecord',
  /** 删除沟通记录外部检查记录 */
  DeleteExternalInspectionRecord: '/ExternalInspectionRecord/DeleteExternalInspectionRecord',
  /** 获取沟通记录外部检查记录列表 */
  GetExternalInspectionRecordList: '/ExternalInspectionRecord/GetExternalInspectionRecordList',
  /** 获取沟通记录外部检查记录列表 */
  GetExternalInspectionRecordSingle: '/ExternalInspectionRecord/GetExternalInspectionRecordSingle',
  /** 作废沟通记录外部检查记录 */
  IsCancelExternalInspectionRecord: '/ExternalInspectionRecord/IsCancelExternalInspectionRecord',
  /** 修改沟通记录外部检查记录 */
  UpdateExternalInspectionRecord: '/ExternalInspectionRecord/UpdateExternalInspectionRecord',
}
export const apiAutoExternalProblem = {
  /** 新增外部复核管理 */
  AddExternalProblem: '/ExternalProblem/AddExternalProblem',
  /** 删除外部复核管理 */
  DeleteExternalProblem: '/ExternalProblem/DeleteExternalProblem',
  /** 获取外部复核管理列表 */
  GetExternalProblemList: '/ExternalProblem/GetExternalProblemList',
  /** 修改外部复核管理 */
  UpdateExternalProblem: '/ExternalProblem/UpdateExternalProblem',
}
export const apiAutoExternalReview = {
  /** 新增外部复核 */
  AddExternalReview: '/ExternalReview/AddExternalReview',
  /** 删除外部复核 */
  DeleteExternalReview: '/ExternalReview/DeleteExternalReview',
  /** 外部复核审批通过 */
  ExamineExternalReview: '/ExternalReview/ExamineExternalReview',
  /** 外部复核提交审批 */
  ExternalReviewSubmit: '/ExternalReview/ExternalReviewSubmit',
  /** 获取外部复核列表 */
  GetExternalReviewList: '/ExternalReview/GetExternalReviewList',
  /** 获取外部复核列表统计 */
  GetExternalReviewListCount: '/ExternalReview/GetExternalReviewListCount',
  /** 获取外部复核---单条 */
  GetExternalReviewSingle: '/ExternalReview/GetExternalReviewSingle',
  /** 外部复核审批不通过 */
  UnExamineExternalReview: '/ExternalReview/UnExamineExternalReview',
  /** 修改外部复核 */
  UpdateExternalReview: '/ExternalReview/UpdateExternalReview',
}
export const apiAutoExternalReviewQuestion = {
  /** 新增外部复核问题 */
  AddExternalReviewQuestion: '/ExternalReviewQuestion/AddExternalReviewQuestion',
  /** 删除外部复核问题 */
  DeleteExternalReviewQuestion: '/ExternalReviewQuestion/DeleteExternalReviewQuestion',
  /** 获取外部复核问题列表 */
  GetExternalReviewQuestionList: '/ExternalReviewQuestion/GetExternalReviewQuestionList',
  /** 获取外部复核问题列表 */
  GetExternalReviewQuestionListFotZhTj: '/ExternalReviewQuestion/GetExternalReviewQuestionListFotZhTj',
  /** 修改外部复核问题 */
  UpdateExternalReviewQuestion: '/ExternalReviewQuestion/UpdateExternalReviewQuestion',
}
export const apiAutoFeedback = {
  /** 新增意见反馈 */
  AddFeedback: '/Feedback/AddFeedback',
  /** 删除意见反馈 */
  DeleteFeedback: '/Feedback/DeleteFeedback',
  /** 获取意见反馈Count */
  GetFeedbackCount: '/Feedback/GetFeedbackCount',
  /** 获取意见反馈列表 */
  GetFeedbackList: '/Feedback/GetFeedbackList',
  /** 修改意见反馈 */
  UpdateFeedback: '/Feedback/UpdateFeedback',
}
export const apiAutoFileItem = {
  /** 新增整体项目资料--批量 */
  AddAddFileItemBath: '/FileItem/AddAddFileItemBath',
  /** 新增整体项目资料 */
  AddFileItem: '/FileItem/AddFileItem',
  /** 删除整体项目资料 */
  DeleteFileItem: '/FileItem/DeleteFileItem',
  /** 获取整体项目资料列表 */
  GetFileItemList: '/FileItem/GetFileItemList',
  /** 修改整体项目资料 */
  UpdateFileItem: '/FileItem/UpdateFileItem',
}
export const apiAutoFileItemType = {
  /** 新增整体项目资料类型 */
  AddFileItemType: '/FileItemType/AddFileItemType',
  /** 删除整体项目资料类型 */
  DeleteFileItemType: '/FileItemType/DeleteFileItemType',
  /** 获取整体项目资料类型列表 */
  GetFileItemTypeList: '/FileItemType/GetFileItemTypeList',
  /** 修改整体项目资料类型 */
  UpdateFileItemType: '/FileItemType/UpdateFileItemType',
}
export const apiAutoFileTemplate = {
  /** 新增文档模板 */
  AddFileTemplate: '/FileTemplate/AddFileTemplate',
  /** 删除文档模板 */
  DeleteFileTemplate: '/FileTemplate/DeleteFileTemplate',
  /** 获取文档模板列表 */
  GetFileTemplateList: '/FileTemplate/GetFileTemplateList',
  /** 修改文档模板 */
  UpdateFileTemplate: '/FileTemplate/UpdateFileTemplate',
}
export const apiAutoFileTypeConfig = {
  /** 新增过程咨询文档类型 */
  AddFileTypeConfig: '/FileTypeConfig/AddFileTypeConfig',
  /** 删除过程咨询文档类型 */
  DeleteFileTypeConfig: '/FileTypeConfig/DeleteFileTypeConfig',
  /** 获取过程咨询文档类型列表 */
  GetFileTypeConfigList: '/FileTypeConfig/GetFileTypeConfigList',
  /** 修改过程咨询文档类型 */
  UpdateFileTypeConfig: '/FileTypeConfig/UpdateFileTypeConfig',
}
export const apiAutoFiledConfig = {
  /** 新增字段配置 */
  AddFiledConfig: '/FiledConfig/AddFiledConfig',
  /** 删除字段配置 */
  DeleteFiledConfig: '/FiledConfig/DeleteFiledConfig',
  /** 获取字段配置 */
  GetFiledConfigList: '/FiledConfig/GetFiledConfigList',
  /** 修改字段配置 */
  UpdateFiledConfig: '/FiledConfig/UpdateFiledConfig',
}
export const apiAutoFkBaseSubsidy = {
  /** 新增基本出差补贴 */
  AddOrUpdateFkBaseSubsidy: '/FkBaseSubsidy/AddOrUpdateFkBaseSubsidy',
  /** 删除基本出差补贴 */
  DeleteFkBaseSubsidy: '/FkBaseSubsidy/DeleteFkBaseSubsidy',
  /** 获取基本出差补贴列表 */
  GetFkBaseSubsidyList: '/FkBaseSubsidy/GetFkBaseSubsidyList',
}
export const apiAutoFkDepartmentResident = {
  /** 新增项目驻地 */
  AddFkDepartmentResident: '/FkDepartmentResident/AddFkDepartmentResident',
  /** 删除项目驻地 */
  DeleteFkDepartmentResident: '/FkDepartmentResident/DeleteFkDepartmentResident',
  /** 获取项目驻地列表 */
  GetFkDepartmentResidentList: '/FkDepartmentResident/GetFkDepartmentResidentList',
  /** 修改项目驻地 */
  UpdateFkDepartmentResident: '/FkDepartmentResident/UpdateFkDepartmentResident',
}
export const apiAutoFkDepartmentRole = {
  /** 新增部门角色 */
  AddFkDepartmentRole: '/FkDepartmentRole/AddFkDepartmentRole',
  /** 删除部门角色 */
  DeleteFkDepartmentRole: '/FkDepartmentRole/DeleteFkDepartmentRole',
  /** 获取部门角色列表 */
  GetFkDepartmentRoleList: '/FkDepartmentRole/GetFkDepartmentRoleList',
  /** 修改部门角色 */
  UpdateFkDepartmentRole: '/FkDepartmentRole/UpdateFkDepartmentRole',
}
export const apiAutoFkPublicStandard = {
  /** 新增或修改私车公用标准 */
  AddOrUpdateFkPublicStandard: '/FkPublicStandard/AddOrUpdateFkPublicStandard',
  /** 删除私车公用标准 */
  DeleteFkPublicStandard: '/FkPublicStandard/DeleteFkPublicStandard',
  /** 获取私车公用标准列表 */
  GetFkPublicStandardList: '/FkPublicStandard/GetFkPublicStandardList',
}
export const apiAutoFkReimbursementRole = {
  /** 新增报销角色 */
  AddFkReimbursementRole: '/FkReimbursementRole/AddFkReimbursementRole',
  /** 删除报销角色 */
  DeleteFkReimbursementRole: '/FkReimbursementRole/DeleteFkReimbursementRole',
  /** 获取报销角色列表 */
  GetFkReimbursementRoleList: '/FkReimbursementRole/GetFkReimbursementRoleList',
}
export const apiAutoFkStayStandard = {
  /** 新增或修改住宿标准 */
  AddOrUpdateFkStayStandard: '/FkStayStandard/AddOrUpdateFkStayStandard',
  /** 删除住宿标准 */
  DeleteFkStayStandard: '/FkStayStandard/DeleteFkStayStandard',
  /** 获取住宿标准列表 */
  GetFkStayStandardList: '/FkStayStandard/GetFkStayStandardList',
}
export const apiAutoFkTrafficStandard = {
  /** 新增交通标准 */
  AddOrUpdateFkTrafficStandard: '/FkTrafficStandard/AddOrUpdateFkTrafficStandard',
  /** 删除交通标准 */
  DeleteFkTrafficStandard: '/FkTrafficStandard/DeleteFkTrafficStandard',
  /** 获取交通标准列表 */
  GetFkTrafficStandardList: '/FkTrafficStandard/GetFkTrafficStandardList',
}
export const apiAutoFkTrafficType = {
  /** 新增交通工具类型配置 */
  AddFkTrafficType: '/FkTrafficType/AddFkTrafficType',
  /** 删除交通工具类型配置 */
  DeleteFkTrafficType: '/FkTrafficType/DeleteFkTrafficType',
  /** 获取交通工具类型配置列表 */
  GetFkTrafficTypeList: '/FkTrafficType/GetFkTrafficTypeList',
  /** 修改交通工具类型配置 */
  UpdateFkTrafficType: '/FkTrafficType/UpdateFkTrafficType',
}
export const apiAutoFkTrafficTypeConfig = {
  /** 新增交通工具配置 */
  AddFkTrafficTypeConfig: '/FkTrafficTypeConfig/AddFkTrafficTypeConfig',
  /** 删除交通工具配置 */
  DeleteFkTrafficTypeConfig: '/FkTrafficTypeConfig/DeleteFkTrafficTypeConfig',
  /** 获取交通工具配置列表 */
  GetFkTrafficTypeConfigList: '/FkTrafficTypeConfig/GetFkTrafficTypeConfigList',
  /** 修改交通工具配置 */
  UpdateFkTrafficTypeConfig: '/FkTrafficTypeConfig/UpdateFkTrafficTypeConfig',
}
export const apiAutoFkTripCityRank = {
  /** 新增出差城市-城市等级 */
  AddFkTripCityRank: '/FkTripCityRank/AddFkTripCityRank',
  /** 删除出差城市-城市等级 */
  DeleteFkTripCityRank: '/FkTripCityRank/DeleteFkTripCityRank',
  /** 获取出差城市-城市等级列表 */
  GetFkTripCityRankList: '/FkTripCityRank/GetFkTripCityRankList',
  /** 修改出差城市-城市等级 */
  UpdateFkTripCityRank: '/FkTripCityRank/UpdateFkTripCityRank',
}
export const apiAutoFkTripMatter = {
  /** 新增出差事由 */
  AddFkTripMatter: '/FkTripMatter/AddFkTripMatter',
  /** 删除出差事由 */
  DeleteFkTripMatter: '/FkTripMatter/DeleteFkTripMatter',
  /** 获取出差事由列表 */
  GetFkTripMatterList: '/FkTripMatter/GetFkTripMatterList',
  /** 修改出差事由 */
  UpdateFkTripMatter: '/FkTripMatter/UpdateFkTripMatter',
}
export const apiAutoFkTripQuality = {
  /** 新增出差性质 */
  AddFkTripQuality: '/FkTripQuality/AddFkTripQuality',
  /** 删除出差性质 */
  DeleteFkTripQuality: '/FkTripQuality/DeleteFkTripQuality',
  /** 获取出差性质列表 */
  GetFkTripQualityList: '/FkTripQuality/GetFkTripQualityList',
  /** 修改出差性质 */
  UpdateFkTripQuality: '/FkTripQuality/UpdateFkTripQuality',
}
export const apiAutoFollowLog = {
  /** 新增跟踪日志 */
  AddFollowLog: '/FollowLog/AddFollowLog',
  /** 作废跟踪日志 */
  CancelFollowLog: '/FollowLog/CancelFollowLog',
  /** 删除跟踪日志 */
  DeleteFollowLog: '/FollowLog/DeleteFollowLog',
  /** 跟踪日志审批通过 */
  ExamineFollowLog: '/FollowLog/ExamineFollowLog',
  /** 导出跟踪日志台账 */
  ExportFollowLog: '/FollowLog/ExportFollowLog',
  /** 跟踪日志审批撤回 */
  FollowLogReCall: '/FollowLog/FollowLogReCall',
  /** 跟踪日志提交审批 */
  FollowLogSubmit: '/FollowLog/FollowLogSubmit',
  /** 获取跟踪日志--统计 */
  GetFollowLogCount: '/FollowLog/GetFollowLogCount',
  /** 获取跟踪日志--工时统计 */
  GetFollowLogCountSg: '/FollowLog/GetFollowLogCountSg',
  /** 获取跟踪日志列表 */
  GetFollowLogList: '/FollowLog/GetFollowLogList',
  /** 获取跟踪日志--单条 */
  GetFollowLogSingle: '/FollowLog/GetFollowLogSingle',
  /** 跟踪日志审批不通过 */
  UnExamineFollowLog: '/FollowLog/UnExamineFollowLog',
  /** 修改跟踪日志 */
  UpdateFollowLog: '/FollowLog/UpdateFollowLog',
}
export const apiAutoFollowLogQuestion = {
  /** 新增跟踪日志问题 */
  AddFollowLogQuestion: '/FollowLogQuestion/AddFollowLogQuestion',
  /** 删除跟踪日志问题 */
  DeleteFollowLogQuestion: '/FollowLogQuestion/DeleteFollowLogQuestion',
  /** 获取跟踪日志问题列表 */
  GetFollowLogQuestionList: '/FollowLogQuestion/GetFollowLogQuestionList',
  /** 判断问题是否回答 */
  JudgeFollowLogQuestion: '/FollowLogQuestion/JudgeFollowLogQuestion',
  /** 修改跟踪日志问题 */
  UpdateFollowLogQuestion: '/FollowLogQuestion/UpdateFollowLogQuestion',
}
export const apiAutoFundPlan = {
  /** 新增资金计划 */
  AddFundPlan: '/FundPlan/AddFundPlan',
  /** 删除资金计划 */
  DeleteFundPlan: '/FundPlan/DeleteFundPlan',
  /** 获取资金计划列表 */
  GetFundPlanList: '/FundPlan/GetFundPlanList',
  /** 获取资金计划列表统计金额 */
  GetFundPlanListCountMoney: '/FundPlan/GetFundPlanListCountMoney',
  /** 修改资金计划 */
  UpdateFundPlan: '/FundPlan/UpdateFundPlan',
}
export const apiAutoGldApi = {
  /** 激活项目 */
  ActivateProject: '/GldApi/ActivateProject',
  /** 完成项目 */
  ArchiveProject: '/GldApi/ArchiveProject',
  /** 创建项目 */
  CreateProject: '/GldApi/CreateProject',
  /** 获取合稿文件 */
  GetGldProFileSingle: '/GldApi/GetGldProFileSingle',
  /** 获取项目基本信息 */
  GetGldProject: '/GldApi/GetGldProject',
  /** 获取本系统暂存广联达项目 */
  GetGldProjectSingle: '/GldApi/GetGldProjectSingle',
  /** 取新建接口 */
  GetInterfaces: '/GldApi/GetInterfaces',
  /** 取单位工程专业类型 */
  GetJobtrades: '/GldApi/GetJobtrades',
  /** 取工程模板分类-列表 */
  GetTempletes: '/GldApi/GetTempletes',
  /** 取模板工程结构 */
  GetTempletestruct: '/GldApi/GetTempletestruct',
  /** 获取项目单体指标 */
  GetUnitZb: '/GldApi/GetUnitZb',
  /** 修改项目 */
  UpdateProject: '/GldApi/UpdateProject',
  /** 本系统暂存广联达项目 */
  saveGldProject: '/GldApi/saveGldProject',
}
export const apiAutoGlobalFile = {
  /** 新增全局文件 */
  AddGlobalFile: '/GlobalFile/AddGlobalFile',
  /** 获取全局文件列表 */
  GetGlobalFileList: '/GlobalFile/GetGlobalFileList',
}
export const apiAutoGoods = {
  /** 新增物品 */
  AddGoods: '/Goods/AddGoods',
  /** 删除物品 */
  DeleteGoods: '/Goods/DeleteGoods',
  /** 获取物品 */
  GetGoodsList: '/Goods/GetGoodsList',
  /** 修改物品 */
  UpdateGoods: '/Goods/UpdateGoods',
}
export const apiAutoGsCalculateFormula = {
  /** 新增计算公式汇总表 */
  AddGsCalculateFormula: '/GsCalculateFormula/AddGsCalculateFormula',
  /** 删除计算公式汇总表 */
  DeleteGsCalculateFormula: '/GsCalculateFormula/DeleteGsCalculateFormula',
  /** 获取计算公式汇总表 */
  GetGsCalculateFormulaList: '/GsCalculateFormula/GetGsCalculateFormulaList',
  /** 修改计算公式汇总表 */
  UpdateGsCalculateFormula: '/GsCalculateFormula/UpdateGsCalculateFormula',
}
export const apiAutoGsCalculateResult = {
  /** 新增计算结果 */
  AddGsCalculateResult: '/GsCalculateResult/AddGsCalculateResult',
  /** 删除计算结果 */
  DeleteGsCalculateResult: '/GsCalculateResult/DeleteGsCalculateResult',
  /** 获取计算结果 */
  GetGsCalculateResultList: '/GsCalculateResult/GetGsCalculateResultList',
  /** 修改计算结果 */
  UpdateGsCalculateResult: '/GsCalculateResult/UpdateGsCalculateResult',
}
export const apiAutoGsCalculateTable = {
  /** 新增计算表汇总表 */
  AddGsCalculateTable: '/GsCalculateTable/AddGsCalculateTable',
  /** 删除计算表汇总表 */
  DeleteGsCalculateTable: '/GsCalculateTable/DeleteGsCalculateTable',
  /** 获取计算表汇总表 */
  GetGsCalculateTableList: '/GsCalculateTable/GetGsCalculateTableList',
  /** 修改计算表汇总表 */
  UpdateGsCalculateTable: '/GsCalculateTable/UpdateGsCalculateTable',
}
export const apiAutoGsCrfConfig = {
  /** 新增插入法配置表 */
  AddGsCrfConfig: '/GsCrfConfig/AddGsCrfConfig',
  /** 删除插入法配置表 */
  DeleteGsCrfConfig: '/GsCrfConfig/DeleteGsCrfConfig',
  /** 获取插入法配置表 */
  GetGsCrfConfigList: '/GsCrfConfig/GetGsCrfConfigList',
  /** 修改插入法配置表 */
  UpdateGsCrfConfig: '/GsCrfConfig/UpdateGsCrfConfig',
}
export const apiAutoGsFddeConfig = {
  /** 新增分档定额法配置表 */
  AddGsFddeConfig: '/GsFddeConfig/AddGsFddeConfig',
  /** 删除分档定额法配置表 */
  DeleteGsFddeConfig: '/GsFddeConfig/DeleteGsFddeConfig',
  /** 获取分档定额法配置表 */
  GetGsFddeConfigList: '/GsFddeConfig/GetGsFddeConfigList',
  /** 修改分档定额法配置表 */
  UpdateGsFddeConfig: '/GsFddeConfig/UpdateGsFddeConfig',
}
export const apiAutoGsFdjsConfig = {
  /** 新增插入法配置表 */
  AddGsFdjsConfig: '/GsFdjsConfig/AddGsFdjsConfig',
  /** 删除插入法配置表 */
  DeleteGsFdjsConfig: '/GsFdjsConfig/DeleteGsFdjsConfig',
  /** 获取插入法配置表 */
  GetGsFdjsConfigList: '/GsFdjsConfig/GetGsFdjsConfigList',
  /** 修改插入法配置表 */
  UpdateGsFdjsConfig: '/GsFdjsConfig/UpdateGsFdjsConfig',
}
export const apiAutoGsFdljfConfig = {
  /** 新增分档累进法配置表 */
  AddGsFdljfConfig: '/GsFdljfConfig/AddGsFdljfConfig',
  /** 删除分档累进法配置表 */
  DeleteGsFdljfConfig: '/GsFdljfConfig/DeleteGsFdljfConfig',
  /** 获取分档累进法配置表 */
  GetGsFdljfConfigList: '/GsFdljfConfig/GetGsFdljfConfigList',
  /** 修改分档累进法配置表 */
  UpdateGsFdljfConfig: '/GsFdljfConfig/UpdateGsFdljfConfig',
}
export const apiAutoGsFieldConfig = {
  /** 新增字段配置表 */
  AddGsFieldConfig: '/GsFieldConfig/AddGsFieldConfig',
  /** 删除字段配置表 */
  DeleteGsFieldConfig: '/GsFieldConfig/DeleteGsFieldConfig',
  /** 获取字段配置表 */
  GetGsFieldConfigList: '/GsFieldConfig/GetGsFieldConfigList',
  /** 修改字段配置表 */
  UpdateGsFieldConfig: '/GsFieldConfig/UpdateGsFieldConfig',
}
export const apiAutoGsFormulaAdditional = {
  /** 新增公式附加项 */
  AddGsFormulaAdditional: '/GsFormulaAdditional/AddGsFormulaAdditional',
  /** 删除公式附加项 */
  DeleteGsFormulaAdditional: '/GsFormulaAdditional/DeleteGsFormulaAdditional',
  /** 获取公式附加项 */
  GetGsFormulaAdditionalList: '/GsFormulaAdditional/GetGsFormulaAdditionalList',
  /** 修改公式附加项 */
  UpdateGsFormulaAdditional: '/GsFormulaAdditional/UpdateGsFormulaAdditional',
}
export const apiAutoGsGroupConfig = {
  /** 新增概算组 */
  AddGsGroupConfig: '/GsGroupConfig/AddGsGroupConfig',
  /** 概算复制组 */
  CopyGsDgroup: '/GsGroupConfig/CopyGsDgroup',
  /** 删除概算组 */
  DeleteGsGroupConfig: '/GsGroupConfig/DeleteGsGroupConfig',
  /** 获取概算组 */
  GetGsGroupConfigList: '/GsGroupConfig/GetGsGroupConfigList',
  /** 修改概算组 */
  UpdateGsGroupConfig: '/GsGroupConfig/UpdateGsGroupConfig',
}
export const apiAutoGsOptionTree = {
  /** 新增可选项树表 */
  AddGsOptionTree: '/GsOptionTree/AddGsOptionTree',
  /** 删除可选项树表 */
  DeleteGsOptionTree: '/GsOptionTree/DeleteGsOptionTree',
  /** 获取可选项树表 */
  GetGsOptionTreeList: '/GsOptionTree/GetGsOptionTreeList',
  /** 修改可选项树表 */
  UpdateGsOptionTree: '/GsOptionTree/UpdateGsOptionTree',
}
export const apiAutoGsProjectNew = {
  /** 新增概算项目基本信息表 */
  AddGsProjectNew: '/GsProjectNew/AddGsProjectNew',
  /** 删除概算项目基本信息表 */
  DeleteGsProjectNew: '/GsProjectNew/DeleteGsProjectNew',
  /** 导出保证金回款记录 */
  ExportCalData: '/GsProjectNew/ExportCalData',
  /** 通过项目CODE，合同CODE 查询对应的 公式附加项List  树结构  以及计算结果 */
  GetCalculateModelInAdminByCode: '/GsProjectNew/GetCalculateModelInAdminByCode',
  /** 计算 */
  GetCalculateResultModelInAdminByCode: '/GsProjectNew/GetCalculateResultModelInAdminByCode',
  /** 获取概算项目基本信息表 */
  GetGsProjectNewList: '/GsProjectNew/GetGsProjectNewList',
  /** 获取概算项目基本信息表单条 */
  GetGsProjectNewSingle: '/GsProjectNew/GetGsProjectNewSingle',
  /** 获取概算项目基本信息表单条ByCode */
  GetGsProjectNewSingleByCode: '/GsProjectNew/GetGsProjectNewSingleByCode',
  /** 修改概算项目基本信息表 */
  UpdateGsProjectNew: '/GsProjectNew/UpdateGsProjectNew',
  /** 修改概算项目基本信息表---修改json */
  UpdateGsProjectNewJson: '/GsProjectNew/UpdateGsProjectNewJson',
}
export const apiAutoGsSummaryProject = {
  /** 新增概算汇总项目 */
  AddGsSummaryProject: '/GsSummaryProject/AddGsSummaryProject',
  /** 计算利息 */
  CalInterest: '/GsSummaryProject/CalInterest',
  /** 删除概算汇总项目 */
  DeleteGsSummaryProject: '/GsSummaryProject/DeleteGsSummaryProject',
  /** 获取概算汇总项目 */
  GetGsSummaryProjectList: '/GsSummaryProject/GetGsSummaryProjectList',
  /** 获取概算汇总项目--单条 */
  GetGsSummaryProjectSingle: '/GsSummaryProject/GetGsSummaryProjectSingle',
  /** 修改概算汇总项目 */
  UpdateGsSummaryProject: '/GsSummaryProject/UpdateGsSummaryProject',
}
export const apiAutoGsSummaryResult = {
  /** 新增概算总汇 */
  AddGsSummaryResult: '/GsSummaryResult/AddGsSummaryResult',
  /** 概算总表--批量计算 */
  AddGsSummaryResultBath: '/GsSummaryResult/AddGsSummaryResultBath',
  /** 删除概算总汇 */
  DeleteGsSummaryResult: '/GsSummaryResult/DeleteGsSummaryResult',
  /** 获取概算总汇--导出 */
  ExportGsSummaryResult: '/GsSummaryResult/ExportGsSummaryResult',
  /** 通过项目CODE，合同CODE 查询对应的 公式附加项List  树结构  以及计算结果 */
  GetGsSummaryResultInAdminByCode: '/GsSummaryResult/GetGsSummaryResultInAdminByCode',
  /** 获取概算总汇 */
  GetGsSummaryResultList: '/GsSummaryResult/GetGsSummaryResultList',
  /** 修改概算总汇 */
  UpdateGsSummaryResult: '/GsSummaryResult/UpdateGsSummaryResult',
}
export const apiAutoHonorBank = {
  /** 新增荣誉库 */
  AddHonorBank: '/HonorBank/AddHonorBank',
  /** 新增荣誉库 */
  BathAddHonorBank: '/HonorBank/BathAddHonorBank',
  /** 新增荣誉库-批量 */
  BathAddHonorBankByPerBath: '/HonorBank/BathAddHonorBankByPerBath',
  /** 删除荣誉库 */
  DeleteHonorBank: '/HonorBank/DeleteHonorBank',
  /** 获取荣誉库列表 */
  GetHonorBankList: '/HonorBank/GetHonorBankList',
  /** 修改荣誉库 */
  UpdateHonorBank: '/HonorBank/UpdateHonorBank',
}
export const apiAutoIndicatorTemplate = {
  /** 新增造价指标模板 */
  AddIndicatorTemplate: '/IndicatorTemplate/AddIndicatorTemplate',
  /** 复制组 */
  CopyIndicatorTemplate: '/IndicatorTemplate/CopyIndicatorTemplate',
  /** 删除造价指标模板 */
  DeleteIndicatorTemplate: '/IndicatorTemplate/DeleteIndicatorTemplate',
  /** 获取造价指标模板列表 */
  GetIndicatorTemplateList: '/IndicatorTemplate/GetIndicatorTemplateList',
  /** 修改造价指标模板 */
  UpdateIndicatorTemplate: '/IndicatorTemplate/UpdateIndicatorTemplate',
}
export const apiAutoInquiryRecord = {
  /** 新增查询单记录信息 */
  AddInquiryRecord: '/InquiryRecord/AddInquiryRecord',
  /** 作废查询单记录信息 */
  CancelInquiryRecord: '/InquiryRecord/CancelInquiryRecord',
  /** 删除查询单记录信息 */
  DeleteInquiryRecord: '/InquiryRecord/DeleteInquiryRecord',
  /** 获取查询单记录信息 */
  GetInquiryRecordList: '/InquiryRecord/GetInquiryRecordList',
  /** 修改查询单记录信息 */
  UpdateInquiryRecord: '/InquiryRecord/UpdateInquiryRecord',
}
export const apiAutoInvoice = {
  /** 新增发票 */
  AddInvoice: '/Invoice/AddInvoice',
  /** 作废发票单 */
  CancelInvoice: '/Invoice/CancelInvoice',
  /**  */
  DealInvoiceData: '/Invoice/DealInvoiceData',
  /**  */
  DealOutputValuePerData: '/Invoice/DealOutputValuePerData',
  /**  */
  DealOutputValuePerData1: '/Invoice/DealOutputValuePerData1',
  /**  */
  DealOutputValuePerDataNew: '/Invoice/DealOutputValuePerDataNew',
  /**  */
  DealOutputValuePerDataNew1: '/Invoice/DealOutputValuePerDataNew1',
  /** 删除发票 */
  DeleteInvoice: '/Invoice/DeleteInvoice',
  /** 审批通过发票单 */
  ExamineInvoice: '/Invoice/ExamineInvoice',
  /** 导出发票台账 */
  ExportInvoice: '/Invoice/ExportInvoice',
  /** 导出产值表 */
  ExportOutputValueTable: '/Invoice/ExportOutputValueTable',
  /** 导出产值表不按部门合并 */
  ExportOutputValueTableByPer: '/Invoice/ExportOutputValueTableByPer',
  /** 获取发票--单条 */
  GetInvoiceByCiIdSingle: '/Invoice/GetInvoiceByCiIdSingle',
  /** 发票统计 */
  GetInvoiceCount: '/Invoice/GetInvoiceCount',
  /** 获取发票列表 */
  GetInvoiceList: '/Invoice/GetInvoiceList',
  /** 获取发票--单条  根据创收id */
  GetInvoiceSingle: '/Invoice/GetInvoiceSingle',
  /** 新增发票 */
  GetInvoiceStateCount: '/Invoice/GetInvoiceStateCount',
  /** 发票统计--金额 */
  GetInvoiceSumMoney: '/Invoice/GetInvoiceSumMoney',
  /** 回退发票 */
  InvoiceBack: '/Invoice/InvoiceBack',
  /** 发票提交审批 */
  InvoiceSubmit: '/Invoice/InvoiceSubmit',
  /** 开票 */
  OpenInvoice: '/Invoice/OpenInvoice',
  /** 普通开票待开票撤回到待提交 */
  RecallCommonInvoiceDtj: '/Invoice/RecallCommonInvoiceDtj',
  /** 快速开票待开票撤回到待提交 */
  RecallInvoiceDtj: '/Invoice/RecallInvoiceDtj',
  /** 数据处理 */
  TbIncomeDep: '/Invoice/TbIncomeDep',
  /** 审批不通过收单 */
  UnExamineInvoice: '/Invoice/UnExamineInvoice',
  /** 修改发票 */
  UpdateInvoice: '/Invoice/UpdateInvoice',
  /** 修改发票号 */
  UpdateIvNo: '/Invoice/UpdateIvNo',
  /** 数据处理 */
  addDep: '/Invoice/addDep',
}
export const apiAutoInvoiceBack = {
  /** 新增发票回款 */
  AddInvoiceBack: '/InvoiceBack/AddInvoiceBack',
  /** 删除发票回款 */
  DeleteInvoiceBack: '/InvoiceBack/DeleteInvoiceBack',
  /** 导出回款 */
  ExportInvoiceBack: '/InvoiceBack/ExportInvoiceBack',
  /** 获取发票回款列表 */
  GetInvoiceBackList: '/InvoiceBack/GetInvoiceBackList',
  /** 回款报表 */
  GetInvoiceBackListExcel: '/InvoiceBack/GetInvoiceBackListExcel',
  /** 获取发票回款--关联人员记录--提成专用 */
  GetInvoiceBackPerCommission: '/InvoiceBack/GetInvoiceBackPerCommission',
  /** 获取发票回款单条 */
  GetInvoiceBackSingle: '/InvoiceBack/GetInvoiceBackSingle',
  /** 发票回款--金额 */
  GetInvoiceBackSumMoney: '/InvoiceBack/GetInvoiceBackSumMoney',
  /** 是否作废发票回款 */
  IsCancelInvoiceBack: '/InvoiceBack/IsCancelInvoiceBack',
  /** 判断发票是否有回款 */
  JudgeInvoiceIsBack: '/InvoiceBack/JudgeInvoiceIsBack',
  /**  */
  Test: '/InvoiceBack/Test',
  /** 修改发票回款 */
  UpdateInvoiceBack: '/InvoiceBack/UpdateInvoiceBack',
}
export const apiAutoInvoiceReimburse = {
  /** 新增发票报销 */
  AddInvoiceReimburse: '/InvoiceReimburse/AddInvoiceReimburse',
  /** 新增发票报销--批量 */
  AddInvoiceReimburseBath: '/InvoiceReimburse/AddInvoiceReimburseBath',
  /** 发票OCR 解析 */
  AnalysisInvoiceReimburseOcr: '/InvoiceReimburse/AnalysisInvoiceReimburseOcr',
  /** 删除发票报销 */
  DeleteInvoiceReimburse: '/InvoiceReimburse/DeleteInvoiceReimburse',
  /** 获取发票报销列表 */
  GetInvoiceReimburseList: '/InvoiceReimburse/GetInvoiceReimburseList',
  /** 获取发票报销列表统计 */
  GetInvoiceReimburseListCount: '/InvoiceReimburse/GetInvoiceReimburseListCount',
  /** 获取发票报销列表统计金额 */
  GetInvoiceReimburseListCountMoney: '/InvoiceReimburse/GetInvoiceReimburseListCountMoney',
  /** 获取单条发票报销 */
  GetInvoiceReimburseSingle: '/InvoiceReimburse/GetInvoiceReimburseSingle',
  /** 发票Ocr */
  InvoiceReimburseOcr: '/InvoiceReimburse/InvoiceReimburseOcr',
  /** 修改发票报销 */
  UpdateInvoiceReimburse: '/InvoiceReimburse/UpdateInvoiceReimburse',
}
export const apiAutoInvoiceReimburseType = {
  /** 新增发票费用类型 */
  AddInvoiceReimburseType: '/InvoiceReimburseType/AddInvoiceReimburseType',
  /** 删除发票费用类型 */
  DeleteInvoiceReimburseType: '/InvoiceReimburseType/DeleteInvoiceReimburseType',
  /** 获取发票费用类型列表 */
  GetInvoiceReimburseTypeList: '/InvoiceReimburseType/GetInvoiceReimburseTypeList',
  /** 修改发票费用类型 */
  UpdateInvoiceReimburseType: '/InvoiceReimburseType/UpdateInvoiceReimburseType',
}
export const apiAutoInvoiceReissue = {
  /** 新增发票重开 */
  AddInvoiceReissue: '/InvoiceReissue/AddInvoiceReissue',
  /** 删除发票重开 */
  DeleteInvoiceReissue: '/InvoiceReissue/DeleteInvoiceReissue',
  /** 合同项目变更审批通过 */
  ExamineInvoiceReissue: '/InvoiceReissue/ExamineInvoiceReissue',
  /** 获取发票重开列表 */
  GetInvoiceReissueList: '/InvoiceReissue/GetInvoiceReissueList',
  /** 获取发票重开列表统计 */
  GetInvoiceReissueListCount: '/InvoiceReissue/GetInvoiceReissueListCount',
  /** 删除发票重开 */
  GetInvoiceReissueSingle: '/InvoiceReissue/GetInvoiceReissueSingle',
  /** 合同项目变更撤回 */
  InvoiceReissueReCall: '/InvoiceReissue/InvoiceReissueReCall',
  /** 合同项目变更提交审批 */
  InvoiceReissueSubmit: '/InvoiceReissue/InvoiceReissueSubmit',
  /** 是否作废合同项目变更 */
  IsCancelInvoiceReissue: '/InvoiceReissue/IsCancelInvoiceReissue',
  /** 判断能否撤回 */
  ProReCallJudge: '/InvoiceReissue/ProReCallJudge',
  /** 合同项目变更审批不通过 */
  UnExamineInvoiceReissue: '/InvoiceReissue/UnExamineInvoiceReissue',
  /** 修改发票重开 */
  UpdateInvoiceReissue: '/InvoiceReissue/UpdateInvoiceReissue',
}
export const apiAutoIvOperationLog = {
  /** 获取发票操作日志 */
  GetIvOperationLogList: '/IvOperationLog/GetIvOperationLogList',
}
export const apiAutoJlCheck = {
  /** 新增检测管理 */
  AddJlCheck: '/JlCheck/AddJlCheck',
  /** 删除检测管理 */
  DeleteJlCheck: '/JlCheck/DeleteJlCheck',
  /** 检测管理审批通过 */
  ExamineJlCheck: '/JlCheck/ExamineJlCheck',
  /** 获取检测管理列表 */
  GetJlCheckList: '/JlCheck/GetJlCheckList',
  /** 获取检测管理列表统计 */
  GetJlCheckListCount: '/JlCheck/GetJlCheckListCount',
  /** 获取检测管理列表 */
  GetJlCheckSingle: '/JlCheck/GetJlCheckSingle',
  /** 检测管理提交审批 */
  SubmitJlCheck: '/JlCheck/SubmitJlCheck',
  /** 检测管理审批不通过 */
  UnExamineJlCheck: '/JlCheck/UnExamineJlCheck',
  /** 修改检测管理 */
  UpdateJlCheck: '/JlCheck/UpdateJlCheck',
}
export const apiAutoJlCheckBeforeAccept = {
  /** 新增验收管理 */
  AddJlCheckBeforeAccept: '/JlCheckBeforeAccept/AddJlCheckBeforeAccept',
  /** 删除验收管理 */
  DeleteJlCheckBeforeAccept: '/JlCheckBeforeAccept/DeleteJlCheckBeforeAccept',
  /** 验收管理审批通过 */
  ExamineJlCheckBeforeAccept: '/JlCheckBeforeAccept/ExamineJlCheckBeforeAccept',
  /** 获取验收管理列表 */
  GetJlCheckBeforeAcceptList: '/JlCheckBeforeAccept/GetJlCheckBeforeAcceptList',
  /** 获取验收管理列表统计 */
  GetJlCheckBeforeAcceptListCount: '/JlCheckBeforeAccept/GetJlCheckBeforeAcceptListCount',
  /** 获取验收管理列表 */
  GetJlCheckBeforeAcceptSingle: '/JlCheckBeforeAccept/GetJlCheckBeforeAcceptSingle',
  /** 验收管理提交审批 */
  SubmitJlCheckBeforeAccept: '/JlCheckBeforeAccept/SubmitJlCheckBeforeAccept',
  /** 验收管理审批不通过 */
  UnExamineJlCheckBeforeAccept: '/JlCheckBeforeAccept/UnExamineJlCheckBeforeAccept',
  /** 修改验收管理 */
  UpdateJlCheckBeforeAccept: '/JlCheckBeforeAccept/UpdateJlCheckBeforeAccept',
}
export const apiAutoJlCommonDocument = {
  /** 创建通用文档pdf合并任务 */
  CreateJlCommonDocumentOssPdfJob: '/JlCommonDocument/CreateJlCommonDocumentOssPdfJob',
  /** 创建通用文档oss压缩任务 */
  CreateJlCommonDocumentOssZipJob: '/JlCommonDocument/CreateJlCommonDocumentOssZipJob',
  /** 处理沟通记录数据 */
  DealGtRecordData: '/JlCommonDocument/DealGtRecordData',
  /** 获取通用文档列表 */
  GetJlCommonDocumentList: '/JlCommonDocument/GetJlCommonDocumentList',
  /** 获取通用文档列表统计 */
  GetJlCommonDocumentListCount: '/JlCommonDocument/GetJlCommonDocumentListCount',
  /** 全过程咨询通用文件左侧结构树 */
  GetJlCommonDocumentListLeftTreeCount: '/JlCommonDocument/GetJlCommonDocumentListLeftTreeCount',
  /** 全过程咨询通用文件左侧结构树人员 */
  GetJlCommonDocumentListLeftTreePerList: '/JlCommonDocument/GetJlCommonDocumentListLeftTreePerList',
  /** 获取通用文档列表 */
  GetJlCommonDocumentSingleByUrl: '/JlCommonDocument/GetJlCommonDocumentSingleByUrl',
}
export const apiAutoJlDangerousEngineering = {
  /** 新增危大工程管理 */
  AddJlDangerousEngineering: '/JlDangerousEngineering/AddJlDangerousEngineering',
  /** 删除危大工程管理 */
  DeleteJlDangerousEngineering: '/JlDangerousEngineering/DeleteJlDangerousEngineering',
  /** 危大工程管理审批通过 */
  ExamineJlDangerousEngineering: '/JlDangerousEngineering/ExamineJlDangerousEngineering',
  /** 获取危大工程管理列表 */
  GetJlDangerousEngineeringList: '/JlDangerousEngineering/GetJlDangerousEngineeringList',
  /** 获取危大工程管理列表统计 */
  GetJlDangerousEngineeringListCount: '/JlDangerousEngineering/GetJlDangerousEngineeringListCount',
  /** 获取危大工程管理列表统计 */
  GetJlDangerousEngineeringListCountExamineState: '/JlDangerousEngineering/GetJlDangerousEngineeringListCountExamineState',
  /** 获取危大工程管理列表 */
  GetJlDangerousEngineeringSingle: '/JlDangerousEngineering/GetJlDangerousEngineeringSingle',
  /** 危大工程管理提交审批 */
  SubmitJlDangerousEngineering: '/JlDangerousEngineering/SubmitJlDangerousEngineering',
  /** 危大工程管理审批不通过 */
  UnExamineJlDangerousEngineering: '/JlDangerousEngineering/UnExamineJlDangerousEngineering',
  /** 修改危大工程管理 */
  UpdateJlDangerousEngineering: '/JlDangerousEngineering/UpdateJlDangerousEngineering',
}
export const apiAutoJlDangerousEngineeringPatrol = {
  /** 新增危大工程巡视管理 */
  AddJlDangerousEngineeringPatrol: '/JlDangerousEngineeringPatrol/AddJlDangerousEngineeringPatrol',
  /** 删除危大工程巡视管理 */
  DeleteJlDangerousEngineeringPatrol: '/JlDangerousEngineeringPatrol/DeleteJlDangerousEngineeringPatrol',
  /** 获取危大工程管理管理巡视列表 */
  GetJlDangerousEngineeringPatrolList: '/JlDangerousEngineeringPatrol/GetJlDangerousEngineeringPatrolList',
  /** 修改危大工程巡视管理 */
  UpdateJlDangerousEngineeringPatrol: '/JlDangerousEngineeringPatrol/UpdateJlDangerousEngineeringPatrol',
}
export const apiAutoJlFileReceive = {
  /** 新增全局监理资料接收 */
  AddJlFileReceive: '/JlFileReceive/AddJlFileReceive',
  /** 删除全局监理资料接收 */
  DeleteJlFileReceive: '/JlFileReceive/DeleteJlFileReceive',
  /** 获取全局监理资料接收列表 */
  GetJlFileReceiveList: '/JlFileReceive/GetJlFileReceiveList',
  /** 修改全局监理资料接收 */
  UpdateJlFileReceive: '/JlFileReceive/UpdateJlFileReceive',
}
export const apiAutoJlFileType = {
  /** 新增全局监理资料类型管理 */
  AddJlFileType: '/JlFileType/AddJlFileType',
  /** 删除全局监理资料类型管理 */
  DeleteJlFileType: '/JlFileType/DeleteJlFileType',
  /** 获取全局监理资料类型管理列表 */
  GetJlFileTypeList: '/JlFileType/GetJlFileTypeList',
  /** 修改全局监理资料类型管理 */
  UpdateJlFileType: '/JlFileType/UpdateJlFileType',
}
export const apiAutoJlFinance = {
  /** 新增财务管理 */
  AddJlFinance: '/JlFinance/AddJlFinance',
  /** 删除财务管理 */
  DeleteJlFinance: '/JlFinance/DeleteJlFinance',
  /** 获取财务管理列表 */
  GetJlFinanceList: '/JlFinance/GetJlFinanceList',
  /**  */
  GetJlFinanceListMoneyCount: '/JlFinance/GetJlFinanceListMoneyCount',
  /** 修改财务管理 */
  UpdateJlFinance: '/JlFinance/UpdateJlFinance',
}
export const apiAutoJlLargeEquipment = {
  /** 新增大型设备管理 */
  AddJlLargeEquipment: '/JlLargeEquipment/AddJlLargeEquipment',
  /** 删除大型设备管理 */
  DeleteJlLargeEquipment: '/JlLargeEquipment/DeleteJlLargeEquipment',
  /** 大型设备管理审批通过 */
  ExamineJlLargeEquipment: '/JlLargeEquipment/ExamineJlLargeEquipment',
  /** 获取大型设备管理列表 */
  GetJlLargeEquipmentList: '/JlLargeEquipment/GetJlLargeEquipmentList',
  /** 获取大型设备管理列表统计 */
  GetJlLargeEquipmentListCount: '/JlLargeEquipment/GetJlLargeEquipmentListCount',
  /** 获取大型设备管理列表统计 */
  GetJlLargeEquipmentListCountExamineState: '/JlLargeEquipment/GetJlLargeEquipmentListCountExamineState',
  /** 获取大型设备管理列表 */
  GetJlLargeEquipmentSingle: '/JlLargeEquipment/GetJlLargeEquipmentSingle',
  /** 大型设备管理提交审批 */
  SubmitJlLargeEquipment: '/JlLargeEquipment/SubmitJlLargeEquipment',
  /** 大型设备管理审批不通过 */
  UnExamineJlLargeEquipment: '/JlLargeEquipment/UnExamineJlLargeEquipment',
  /** 修改大型设备管理 */
  UpdateJlLargeEquipment: '/JlLargeEquipment/UpdateJlLargeEquipment',
}
export const apiAutoJlLargeEquipmentWb = {
  /** 新增大型设备定期维保管理 */
  AddJlLargeEquipmentWb: '/JlLargeEquipmentWb/AddJlLargeEquipmentWb',
  /** 删除大型设备定期维保管理 */
  DeleteJlLargeEquipmentWb: '/JlLargeEquipmentWb/DeleteJlLargeEquipmentWb',
  /** 获取大型设备定期维保管理列表 */
  GetJlLargeEquipmentWbList: '/JlLargeEquipmentWb/GetJlLargeEquipmentWbList',
  /** 修改大型设备定期维保管理 */
  UpdateJlLargeEquipmentWb: '/JlLargeEquipmentWb/UpdateJlLargeEquipmentWb',
}
export const apiAutoJlMaterialMobilization = {
  /** 新增材料进场 */
  AddJlMaterialMobilization: '/JlMaterialMobilization/AddJlMaterialMobilization',
  /** 删除材料进场 */
  DeleteJlMaterialMobilization: '/JlMaterialMobilization/DeleteJlMaterialMobilization',
  /** 材料进场管理审批通过 */
  ExamineJlMaterialMobilization: '/JlMaterialMobilization/ExamineJlMaterialMobilization',
  /** 获取材料进场列表 */
  GetJlMaterialMobilizationList: '/JlMaterialMobilization/GetJlMaterialMobilizationList',
  /** 获取材料进场列表统计 */
  GetJlMaterialMobilizationListCount: '/JlMaterialMobilization/GetJlMaterialMobilizationListCount',
  /** 获取材料进场列表 */
  GetJlMaterialMobilizationSingle: '/JlMaterialMobilization/GetJlMaterialMobilizationSingle',
  /** 材料进场管理提交审批 */
  SubmitJlMaterialMobilization: '/JlMaterialMobilization/SubmitJlMaterialMobilization',
  /** 材料进场管理审批不通过 */
  UnExamineJlMaterialMobilization: '/JlMaterialMobilization/UnExamineJlMaterialMobilization',
  /** 修改材料进场 */
  UpdateJlMaterialMobilization: '/JlMaterialMobilization/UpdateJlMaterialMobilization',
}
export const apiAutoJlMeetingCommunication = {
  /** 新增会审记录 */
  AddJlMeetingCommunication: '/JlMeetingCommunication/AddJlMeetingCommunication',
  /** 删除会审记录 */
  DeleteJlMeetingCommunication: '/JlMeetingCommunication/DeleteJlMeetingCommunication',
  /** 获取会审记录列表 */
  GetJlMeetingCommunicationList: '/JlMeetingCommunication/GetJlMeetingCommunicationList',
  /** 修改会审记录 */
  UpdateJlMeetingCommunication: '/JlMeetingCommunication/UpdateJlMeetingCommunication',
}
export const apiAutoJlMeterialInfo = {
  /** 新增材料信息 */
  AddJlMeterialInfo: '/JlMeterialInfo/AddJlMeterialInfo',
  /** 删除材料信息 */
  DeleteJlMeterialInfo: '/JlMeterialInfo/DeleteJlMeterialInfo',
  /** 材料信息管理审批通过 */
  ExamineJlMeterialInfo: '/JlMeterialInfo/ExamineJlMeterialInfo',
  /** 获取材料信息列表 */
  GetJlMeterialInfoList: '/JlMeterialInfo/GetJlMeterialInfoList',
  /** 获取材料信息列表统计 */
  GetJlMeterialInfoListCount: '/JlMeterialInfo/GetJlMeterialInfoListCount',
  /** 获取材料信息列表 */
  GetJlMeterialInfoSingle: '/JlMeterialInfo/GetJlMeterialInfoSingle',
  /** 材料信息管理提交审批 */
  SubmitJlMeterialInfo: '/JlMeterialInfo/SubmitJlMeterialInfo',
  /** 材料信息管理审批不通过 */
  UnExamineJlMeterialInfo: '/JlMeterialInfo/UnExamineJlMeterialInfo',
  /** 修改材料信息 */
  UpdateJlMeterialInfo: '/JlMeterialInfo/UpdateJlMeterialInfo',
}
export const apiAutoJlOnSiteProblem = {
  /** 新增现场问题 */
  AddJlOnSiteProblem: '/JlOnSiteProblem/AddJlOnSiteProblem',
  /** 删除现场问题 */
  DeleteJlOnSiteProblem: '/JlOnSiteProblem/DeleteJlOnSiteProblem',
  /** 现场问题管理审批通过 */
  ExamineJlOnSiteProblem: '/JlOnSiteProblem/ExamineJlOnSiteProblem',
  /** 获取现场问题列表 */
  GetJlOnSiteProblemList: '/JlOnSiteProblem/GetJlOnSiteProblemList',
  /** 获取现场问题列表统计 */
  GetJlOnSiteProblemListCount: '/JlOnSiteProblem/GetJlOnSiteProblemListCount',
  /** 获取现场问题列表统计 */
  GetJlOnSiteProblemListCountExamineState: '/JlOnSiteProblem/GetJlOnSiteProblemListCountExamineState',
  /** 获取现场问题列表 */
  GetJlOnSiteProblemSingle: '/JlOnSiteProblem/GetJlOnSiteProblemSingle',
  /** 现场问题管理提交审批 */
  SubmitJlOnSiteProblem: '/JlOnSiteProblem/SubmitJlOnSiteProblem',
  /** 现场问题管理审批不通过 */
  UnExamineJlOnSiteProblem: '/JlOnSiteProblem/UnExamineJlOnSiteProblem',
  /** 修改现场问题 */
  UpdateJlOnSiteProblem: '/JlOnSiteProblem/UpdateJlOnSiteProblem',
}
export const apiAutoJlOutCheckRecord = {
  /** 新增外部检查记录 */
  AddJlOutCheckRecord: '/JlOutCheckRecord/AddJlOutCheckRecord',
  /** 删除外部检查记录 */
  DeleteJlOutCheckRecord: '/JlOutCheckRecord/DeleteJlOutCheckRecord',
  /** 获取外部检查记录列表 */
  GetJlOutCheckRecordList: '/JlOutCheckRecord/GetJlOutCheckRecordList',
  /** 修改外部检查记录 */
  UpdateJlOutCheckRecord: '/JlOutCheckRecord/UpdateJlOutCheckRecord',
}
export const apiAutoJlPatrol = {
  /** 新增巡视管理 */
  AddJlPatrol: '/JlPatrol/AddJlPatrol',
  /** 删除巡视管理 */
  DeleteJlPatrol: '/JlPatrol/DeleteJlPatrol',
  /** 巡视管理审批通过 */
  ExamineJlPatrol: '/JlPatrol/ExamineJlPatrol',
  /** 获取巡视管理列表 */
  GetJlPatrolList: '/JlPatrol/GetJlPatrolList',
  /** 获取巡视管理列表统计 */
  GetJlPatrolListCount: '/JlPatrol/GetJlPatrolListCount',
  /** 获取巡视管理列表 */
  GetJlPatrolSingle: '/JlPatrol/GetJlPatrolSingle',
  /** 巡视管理提交审批 */
  SubmitJlPatrol: '/JlPatrol/SubmitJlPatrol',
  /** 巡视管理审批不通过 */
  UnExamineJlPatrol: '/JlPatrol/UnExamineJlPatrol',
  /** 修改巡视管理 */
  UpdateJlPatrol: '/JlPatrol/UpdateJlPatrol',
}
export const apiAutoJlProFile = {
  /** 新增全过程咨询项目内监理资料管理 */
  AddJlProFile: '/JlProFile/AddJlProFile',
  /** 批量新增全过程咨询项目内监理资料管理 */
  BathAddJlProFile: '/JlProFile/BathAddJlProFile',
  /** 删除全过程咨询项目内监理资料管理 */
  DeleteJlProFile: '/JlProFile/DeleteJlProFile',
  /** 获取全过程咨询项目内监理资料管理列表 */
  GetJlProFileList: '/JlProFile/GetJlProFileList',
  /** 修改全过程咨询项目内监理资料管理 */
  UpdateJlProFile: '/JlProFile/UpdateJlProFile',
}
export const apiAutoJlSelfEquipment = {
  /** 新增自有设备管理 */
  AddJlSelfEquipment: '/JlSelfEquipment/AddJlSelfEquipment',
  /** 删除自有设备管理 */
  DeleteJlSelfEquipment: '/JlSelfEquipment/DeleteJlSelfEquipment',
  /** 自有设备管理审批通过 */
  ExamineJlSelfEquipment: '/JlSelfEquipment/ExamineJlSelfEquipment',
  /** 获取自有设备管理列表 */
  GetJlSelfEquipmentList: '/JlSelfEquipment/GetJlSelfEquipmentList',
  /** 获取自有设备管理列表统计 */
  GetJlSelfEquipmentListCount: '/JlSelfEquipment/GetJlSelfEquipmentListCount',
  /** 获取自有设备管理列表统计 */
  GetJlSelfEquipmentListCountExamineState: '/JlSelfEquipment/GetJlSelfEquipmentListCountExamineState',
  /** 获取自有设备管理列表 */
  GetJlSelfEquipmentSingle: '/JlSelfEquipment/GetJlSelfEquipmentSingle',
  /** 自有设备管理提交审批 */
  SubmitJlSelfEquipment: '/JlSelfEquipment/SubmitJlSelfEquipment',
  /** 自有设备管理审批不通过 */
  UnExamineJlSelfEquipment: '/JlSelfEquipment/UnExamineJlSelfEquipment',
  /** 修改自有设备管理 */
  UpdateJlSelfEquipment: '/JlSelfEquipment/UpdateJlSelfEquipment',
}
export const apiAutoJlSideStation = {
  /** 新增旁站管理 */
  AddJlSideStation: '/JlSideStation/AddJlSideStation',
  /** 删除旁站管理 */
  DeleteJlSideStation: '/JlSideStation/DeleteJlSideStation',
  /** 旁站管理审批通过 */
  ExamineJlSideStation: '/JlSideStation/ExamineJlSideStation',
  /** 获取旁站管理列表 */
  GetJlSideStationList: '/JlSideStation/GetJlSideStationList',
  /** 获取旁站管理列表 */
  GetJlSideStationListCount: '/JlSideStation/GetJlSideStationListCount',
  /** 获取旁站管理列表 */
  GetJlSideStationSingle: '/JlSideStation/GetJlSideStationSingle',
  /** 旁站管理提交审批 */
  SubmitJlSideStation: '/JlSideStation/SubmitJlSideStation',
  /** 旁站管理审批不通过 */
  UnExamineJlSideStation: '/JlSideStation/UnExamineJlSideStation',
  /** 修改旁站管理 */
  UpdateJlSideStation: '/JlSideStation/UpdateJlSideStation',
}
export const apiAutoJlStartWork = {
  /** 新增开工管理 */
  AddJlStartWork: '/JlStartWork/AddJlStartWork',
  /** 删除开工管理 */
  DeleteJlStartWork: '/JlStartWork/DeleteJlStartWork',
  /** 获取开工管理列表 */
  GetJlStartWorkList: '/JlStartWork/GetJlStartWorkList',
  /** 获取开工管理列表 */
  GetJlStartWorkSingle: '/JlStartWork/GetJlStartWorkSingle',
  /** 是否作废开工管理 */
  IsCancelJlStartWork: '/JlStartWork/IsCancelJlStartWork',
  /** 修改开工管理 */
  UpdateJlStartWork: '/JlStartWork/UpdateJlStartWork',
}
export const apiAutoJlWitnessSampling = {
  /** 新增见证取样 */
  AddJlWitnessSampling: '/JlWitnessSampling/AddJlWitnessSampling',
  /** 删除见证取样 */
  DeleteJlWitnessSampling: '/JlWitnessSampling/DeleteJlWitnessSampling',
  /** 见证取样管理审批通过 */
  ExamineJlWitnessSampling: '/JlWitnessSampling/ExamineJlWitnessSampling',
  /** 获取见证取样列表 */
  GetJlWitnessSamplingList: '/JlWitnessSampling/GetJlWitnessSamplingList',
  /** 获取见证取样列表统计 */
  GetJlWitnessSamplingListCount: '/JlWitnessSampling/GetJlWitnessSamplingListCount',
  /** 获取见证取样列表 */
  GetJlWitnessSamplingSingle: '/JlWitnessSampling/GetJlWitnessSamplingSingle',
  /** 见证取样管理提交审批 */
  SubmitJlWitnessSampling: '/JlWitnessSampling/SubmitJlWitnessSampling',
  /** 见证取样管理审批不通过 */
  UnExamineJlWitnessSampling: '/JlWitnessSampling/UnExamineJlWitnessSampling',
  /** 修改见证取样 */
  UpdateJlWitnessSampling: '/JlWitnessSampling/UpdateJlWitnessSampling',
}
export const apiAutoJlWorkDivision = {
  /** 新增监理工作划分 */
  AddJlWorkDivision: '/JlWorkDivision/AddJlWorkDivision',
  /** 删除监理工作划分 */
  DeleteJlWorkDivision: '/JlWorkDivision/DeleteJlWorkDivision',
  /** 获取监理工作划分列表 */
  GetJlWorkDivisionList: '/JlWorkDivision/GetJlWorkDivisionList',
  /** 修改监理工作划分 */
  UpdateJlWorkDivision: '/JlWorkDivision/UpdateJlWorkDivision',
}
export const apiAutoJlWorkDivisionTree = {
  /** 新增监理工作划分左侧树 */
  AddJlWorkDivisionTree: '/JlWorkDivisionTree/AddJlWorkDivisionTree',
  /** 删除监理工作划分左侧树 */
  DeleteJlWorkDivisionTree: '/JlWorkDivisionTree/DeleteJlWorkDivisionTree',
  /** 获取监理工作划分左侧树列表 */
  GetJlWorkDivisionTreeList: '/JlWorkDivisionTree/GetJlWorkDivisionTreeList',
  /** 修改监理工作划分左侧树 */
  UpdateJlWorkDivisionTree: '/JlWorkDivisionTree/UpdateJlWorkDivisionTree',
}
export const apiAutoJointReviewRecord = {
  /** 新增沟通记录会审记录 */
  AddJointReviewRecord: '/JointReviewRecord/AddJointReviewRecord',
  /** 删除沟通记录会审记录 */
  DeleteJointReviewRecord: '/JointReviewRecord/DeleteJointReviewRecord',
  /** 获取沟通记录会审记录列表 */
  GetJointReviewRecordList: '/JointReviewRecord/GetJointReviewRecordList',
  /** 获取沟通记录会审记录列表 */
  GetJointReviewRecordSingle: '/JointReviewRecord/GetJointReviewRecordSingle',
  /** 作废沟通记录会审记录 */
  IsCancelJointReviewRecord: '/JointReviewRecord/IsCancelJointReviewRecord',
  /** 修改沟通记录会审记录 */
  UpdateJointReviewRecord: '/JointReviewRecord/UpdateJointReviewRecord',
}
export const apiAutoKnowledgeZoneConfig = {
  /** 新增知识专区配置 */
  AddKnowledgeZoneConfig: '/KnowledgeZoneConfig/AddKnowledgeZoneConfig',
  /** 删除知识专区配置 */
  DeleteKnowledgeZoneConfig: '/KnowledgeZoneConfig/DeleteKnowledgeZoneConfig',
  /** 获取知识专区配置列表 */
  GetKnowledgeZoneConfigList: '/KnowledgeZoneConfig/GetKnowledgeZoneConfigList',
  /** 修改知识专区配置 */
  UpdateKnowledgeZoneConfig: '/KnowledgeZoneConfig/UpdateKnowledgeZoneConfig',
}
export const apiAutoLaborContract = {
  /** 新增劳动合同管理--合同 */
  AddLaborContract: '/LaborContract/AddLaborContract',
  /** 作废或取消劳动合同管理--合同 */
  CancelLaborContract: '/LaborContract/CancelLaborContract',
  /** 删除劳动合同管理--合同 */
  DeleteLaborContract: '/LaborContract/DeleteLaborContract',
  /** 导出劳动合同管理列表 */
  ExportLaborContractManageList: '/LaborContract/ExportLaborContractManageList',
  /** 获取劳动合同管理--合同列表 */
  GetLaborContractList: '/LaborContract/GetLaborContractList',
  /** 获取劳动合同管理列表 */
  GetLaborContractManageList: '/LaborContract/GetLaborContractManageList',
  /** 获取劳动合同管理列表统计 */
  GetLaborContractManageListCount: '/LaborContract/GetLaborContractManageListCount',
  /** 获取劳动合同管理--合同列表新 */
  GetLaborContractToDoList: '/LaborContract/GetLaborContractToDoList',
  /** 是否存在生效中的数据 */
  IsExistLaborContract: '/LaborContract/IsExistLaborContract',
  /**  */
  TbLaborContractManageListData: '/LaborContract/TbLaborContractManageListData',
  /** 修改劳动合同管理--合同 */
  UpdateLaborContract: '/LaborContract/UpdateLaborContract',
}
export const apiAutoLeaveRecord = {
  /** 新增请假管理 */
  AddLeaveRecord: '/LeaveRecord/AddLeaveRecord',
  /** 作废请假管理 */
  CancelLeaveRecord: '/LeaveRecord/CancelLeaveRecord',
  /** 删除请假管理 */
  DeleteLeaveRecord: '/LeaveRecord/DeleteLeaveRecord',
  /** 请假管理审批通过 */
  ExamineLeaveRecord: '/LeaveRecord/ExamineLeaveRecord',
  /** 请假管理审批通过--销假 */
  ExamineLeaveRecordXj: '/LeaveRecord/ExamineLeaveRecordXj',
  /** 导出请假管理列表 */
  ExportLeaveRecordList: '/LeaveRecord/ExportLeaveRecordList',
  /** 获取当年请假时长 */
  GetBurningTimeYear: '/LeaveRecord/GetBurningTimeYear',
  /** 获取请假管理列表 */
  GetLeaveRecordList: '/LeaveRecord/GetLeaveRecordList',
  /** 获取请假管理列表统计 */
  GetLeaveRecordListCount: '/LeaveRecord/GetLeaveRecordListCount',
  /** 获取请假管理列表统计--类型统计 */
  GetLeaveRecordListCountGroupType: '/LeaveRecord/GetLeaveRecordListCountGroupType',
  /** 获取请假管理--单条 */
  GetLeaveRecordSingle: '/LeaveRecord/GetLeaveRecordSingle',
  /** 请假管理提交审批 */
  LeaveRecordSubmit: '/LeaveRecord/LeaveRecordSubmit',
  /** 请假管理提交审批--销假 */
  LeaveRecordSubmitXj: '/LeaveRecord/LeaveRecordSubmitXj',
  /** 请假管理审批不通过 */
  UnExamineLeaveRecord: '/LeaveRecord/UnExamineLeaveRecord',
  /** 请假管理审批不通过--销假 */
  UnExamineLeaveRecordXj: '/LeaveRecord/UnExamineLeaveRecordXj',
  /** 修改请假管理 */
  UpdateLeaveRecord: '/LeaveRecord/UpdateLeaveRecord',
}
export const apiAutoLiOperationLog = {
  /** 获取借款操作日志 */
  GetLiOperationLogList: '/LiOperationLog/GetLiOperationLogList',
}
export const apiAutoLoanBack = {
  /** 新增借款回款 */
  AddLoanBack: '/LoanBack/AddLoanBack',
  /** 删除借款回款 */
  DeleteLoanBack: '/LoanBack/DeleteLoanBack',
  /** 借款回款统计 */
  GetLoanBackCount: '/LoanBack/GetLoanBackCount',
  /** 获取借款回款列表 */
  GetLoanBackList: '/LoanBack/GetLoanBackList',
  /** 获取借款回款单个 */
  GetLoanBackSingle: '/LoanBack/GetLoanBackSingle',
  /** 是否作废借款回款 */
  IsCancelLoanBack: '/LoanBack/IsCancelLoanBack',
  /** 接收借款 */
  ReceiveLoanBack: '/LoanBack/ReceiveLoanBack',
  /** 打回借款 */
  RepulseLoanBack: '/LoanBack/RepulseLoanBack',
  /** 修改借款回款 */
  UpdateLoanBack: '/LoanBack/UpdateLoanBack',
}
export const apiAutoLoanConfig = {
  /** 获取借款配置 */
  GetLoanConfigSingle: '/LoanConfig/GetLoanConfigSingle',
  /** 修改借款配置 */
  UpdateLoanConfig: '/LoanConfig/UpdateLoanConfig',
}
export const apiAutoLoanDepConfig = {
  /** 新增借款部门配置 */
  AddLoanDepConfig: '/LoanDepConfig/AddLoanDepConfig',
  /** 删除借款部门配置 */
  DeleteLoanDepConfig: '/LoanDepConfig/DeleteLoanDepConfig',
  /** 获取借款部门配置列表 */
  GetLoanDepConfigList: '/LoanDepConfig/GetLoanDepConfigList',
  /** 修改借款部门配置 */
  UpdateLoanDepConfig: '/LoanDepConfig/UpdateLoanDepConfig',
}
export const apiAutoLoanInfo = {
  /** 新增借款 */
  AddLoanInfo: '/LoanInfo/AddLoanInfo',
  /** 作废借款 */
  CancelLoanInfo: '/LoanInfo/CancelLoanInfo',
  /** 删除借款 */
  DeleteLoanInfo: '/LoanInfo/DeleteLoanInfo',
  /** 借款审批通过 */
  ExamineLoanInfo: '/LoanInfo/ExamineLoanInfo',
  /** 借款统计 */
  GetLoanInfoCount: '/LoanInfo/GetLoanInfoCount',
  /** 借款统计金额 */
  GetLoanInfoCountMoney: '/LoanInfo/GetLoanInfoCountMoney',
  /** 获取借款列表 */
  GetLoanInfoList: '/LoanInfo/GetLoanInfoList',
  /** 获取借款---单条 */
  GetLoanInfoSingle: '/LoanInfo/GetLoanInfoSingle',
  /** 借款提交审批 */
  LoanInfoSubmit: '/LoanInfo/LoanInfoSubmit',
  /** 放款 */
  MakeLoan: '/LoanInfo/MakeLoan',
  /** 借款审批不通过 */
  UnExamineLoanInfo: '/LoanInfo/UnExamineLoanInfo',
  /** 修改借款 */
  UpdateLoanInfo: '/LoanInfo/UpdateLoanInfo',
}
export const apiAutoLoanType = {
  /** 新增借款类型 */
  AddLoanType: '/LoanType/AddLoanType',
  /** 删除借款类型 */
  DeleteLoanType: '/LoanType/DeleteLoanType',
  /** 获取借款类型列表 */
  GetLoanTypeList: '/LoanType/GetLoanTypeList',
  /** 修改借款类型 */
  UpdateLoanType: '/LoanType/UpdateLoanType',
}
export const apiAutoMajorType = {
  /** 新增专业类型 */
  AddMajorType: '/MajorType/AddMajorType',
  /** 删除专业类型 */
  DeleteMajorType: '/MajorType/DeleteMajorType',
  /** 获取专业类型 */
  GetMajorTypeList: '/MajorType/GetMajorTypeList',
  /** 修改专业类型 */
  UpdateMajorType: '/MajorType/UpdateMajorType',
}
export const apiAutoManualInquiry = {
  /** 新增人工询价--批量 */
  AddBathManualInquiry: '/ManualInquiry/AddBathManualInquiry',
  /** 新增人工询价 */
  AddManualInquiry: '/ManualInquiry/AddManualInquiry',
  /** 复制人工询价 */
  CopyManualInquiry: '/ManualInquiry/CopyManualInquiry',
  /** 删除人工询价 */
  DeleteManualInquiry: '/ManualInquiry/DeleteManualInquiry',
  /** 获取人工询价 */
  GetManualInquiryList: '/ManualInquiry/GetManualInquiryList',
  /** 查询单条人工询价 */
  GetManualInquirySingle: '/ManualInquiry/GetManualInquirySingle',
  /** 修改人工询价 */
  UpdateManualInquiry: '/ManualInquiry/UpdateManualInquiry',
  /** 无价材料库上传 */
  UploadManualInquiry: '/ManualInquiry/UploadManualInquiry',
}
export const apiAutoMeeting = {
  /** 新增会议 */
  AddMeeting: '/Meeting/AddMeeting',
  /** 取消预约 */
  CancelMeetingYy: '/Meeting/CancelMeetingYy',
  /** 删除会议 */
  DeleteMeeting: '/Meeting/DeleteMeeting',
  /** 会议审批通过--预约 */
  DistributionMeeting: '/Meeting/DistributionMeeting',
  /** 会议审批通过 */
  ExamineMeeting: '/Meeting/ExamineMeeting',
  /** 获取会议Count */
  GetMeetingCount: '/Meeting/GetMeetingCount',
  /** 获取会议列表 */
  GetMeetingList: '/Meeting/GetMeetingList',
  /** 获取会议列表使用情况 */
  GetMeetingListYy: '/Meeting/GetMeetingListYy',
  /** 获取会议单条 */
  GetMeetingSingle: '/Meeting/GetMeetingSingle',
  /** 会议撤回 */
  MeetingReCall: '/Meeting/MeetingReCall',
  /** 会议提交审批 */
  SubmitMeeting: '/Meeting/SubmitMeeting',
  /** 会议审批不通过 */
  UnExamineMeeting: '/Meeting/UnExamineMeeting',
  /** 修改会议 */
  UpdateMeeting: '/Meeting/UpdateMeeting',
}
export const apiAutoMeetingCommunication = {
  /** 新增会议沟通 */
  AddMeetingCommunication: '/MeetingCommunication/AddMeetingCommunication',
  /** 删除会议沟通 */
  DeleteMeetingCommunication: '/MeetingCommunication/DeleteMeetingCommunication',
  /** 获取会议沟通列表 */
  GetMeetingCommunicationList: '/MeetingCommunication/GetMeetingCommunicationList',
  /** 获取会议沟通单条 */
  GetMeetingCommunicationSingle: '/MeetingCommunication/GetMeetingCommunicationSingle',
  /** 作废或者取消作废会议沟通 */
  IsCancelMeetingCommunication: '/MeetingCommunication/IsCancelMeetingCommunication',
  /** 修改会议沟通 */
  UpdateMeetingCommunication: '/MeetingCommunication/UpdateMeetingCommunication',
}
export const apiAutoMeetingRoom = {
  /** 新增会议室 */
  AddMeetingRoom: '/MeetingRoom/AddMeetingRoom',
  /** 删除会议室 */
  DeleteMeetingRoom: '/MeetingRoom/DeleteMeetingRoom',
  /** 获取会议室列表 */
  GetMeetingRoomList: '/MeetingRoom/GetMeetingRoomList',
  /** 获取会议室单条 */
  GetMeetingRoomSingle: '/MeetingRoom/GetMeetingRoomSingle',
  /** 修改会议室 */
  UpdateMeetingRoom: '/MeetingRoom/UpdateMeetingRoom',
}
export const apiAutoMinIo = {
  /**  */
  DownloadGetObject: '/MinIo/DownloadGetObject',
  /**  */
  PresignedGetObject: '/MinIo/PresignedGetObject',
  /**  */
  PresignedGetObjectUrl: '/MinIo/PresignedGetObjectUrl',
  /**  */
  PresignedPutObject: '/MinIo/PresignedPutObject',
}
export const apiAutoMobilephone = {
  /** 新增手机 */
  AddMobilephone: '/Mobilephone/AddMobilephone',
  /** 删除手机 */
  DeleteMobilephone: '/Mobilephone/DeleteMobilephone',
  /** 导出手机数据 */
  ExportMobilephoneList: '/Mobilephone/ExportMobilephoneList',
  /** 获取手机列表 */
  GetMobilephoneList: '/Mobilephone/GetMobilephoneList',
  /** 获取手机单条 */
  GetMobilephoneSingle: '/Mobilephone/GetMobilephoneSingle',
  /** 修改手机 */
  UpdateMobilephone: '/Mobilephone/UpdateMobilephone',
}
export const apiAutoNewBigScreen = {
  /** 作业人员统计 */
  GetCompilerStatistics: '/NewBigScreen/GetCompilerStatistics',
  /** 作业人员统计-详情 */
  GetCompilerStatisticsDetail: '/NewBigScreen/GetCompilerStatisticsDetail',
  /** 机构统计-一级问题分布情况 */
  GetOrganizeStatisticsFirstQuestionDistribution: '/NewBigScreen/GetOrganizeStatisticsFirstQuestionDistribution',
  /** 机构统计--项目概况 */
  GetOrganizeStatisticsProjectOverview: '/NewBigScreen/GetOrganizeStatisticsProjectOverview',
  /** 机构统计--审核类项目净核减率，审核类项目三级审核率，编制类项目三级审核率 */
  GetOrganizeStatisticsRateSort: '/NewBigScreen/GetOrganizeStatisticsRateSort',
  /** 机构统计-二级问题分布情况 */
  GetOrganizeStatisticsSecQuestionDistribution: '/NewBigScreen/GetOrganizeStatisticsSecQuestionDistribution',
  /** 项目统计 */
  GetProjectStatistics: '/NewBigScreen/GetProjectStatistics',
  /** 审核人员统计 */
  GetSecondaryReviewerStatistics: '/NewBigScreen/GetSecondaryReviewerStatistics',
}
export const apiAutoNews = {
  /** 新增消息通知 */
  AddNews: '/News/AddNews',
  /** 删除消息通知 */
  DeleteNews: '/News/DeleteNews',
  /** 获取消息通知列表 */
  GetNewsList: '/News/GetNewsList',
  /** 获取消息提醒列表 */
  GetNewsRemindList: '/News/GetNewsRemindList',
  /** 获取个人消息提醒列表 */
  GetNewsRemindListByOwn: '/News/GetNewsRemindListByOwn',
  /** 个人已读未读统计 */
  GetNewsRemindListByOwnCount: '/News/GetNewsRemindListByOwnCount',
  /** 已读未读统计 */
  GetNewsRemindListCount: '/News/GetNewsRemindListCount',
  /** 设为已读 */
  UpDateNewsState: '/News/UpDateNewsState',
  /** 修改消息通知 */
  UpdateNews: '/News/UpdateNews',
}
export const apiAutoOfficeSupplies = {
  /** 新增办公用品 */
  AddOfficeSupplies: '/OfficeSupplies/AddOfficeSupplies',
  /** 打回办公用品 */
  BackOfficeSupplies: '/OfficeSupplies/BackOfficeSupplies',
  /** 作废和取消 */
  CanceleOrRestoreOfficeSupplies: '/OfficeSupplies/CanceleOrRestoreOfficeSupplies',
  /** 删除办公用品 */
  DeleteOfficeSupplies: '/OfficeSupplies/DeleteOfficeSupplies',
  /** 分配办公用品 */
  DistributionOfficeSupplies: '/OfficeSupplies/DistributionOfficeSupplies',
  /** 办公用品审批通过 */
  ExamineOfficeSupplies: '/OfficeSupplies/ExamineOfficeSupplies',
  /** 获取办公用品Count */
  GetOfficeSuppliesCount: '/OfficeSupplies/GetOfficeSuppliesCount',
  /** 获取办公用品分配首页列表 */
  GetOfficeSuppliesDistributionIndexList: '/OfficeSupplies/GetOfficeSuppliesDistributionIndexList',
  /** 获取办公用品分配列表 */
  GetOfficeSuppliesDistributionList: '/OfficeSupplies/GetOfficeSuppliesDistributionList',
  /** 获取办公用品归还首页列表 */
  GetOfficeSuppliesGiveBackIndexList: '/OfficeSupplies/GetOfficeSuppliesGiveBackIndexList',
  /** 获取办公用品归还列表 */
  GetOfficeSuppliesGiveBackList: '/OfficeSupplies/GetOfficeSuppliesGiveBackList',
  /** 获取办公用品列表 */
  GetOfficeSuppliesList: '/OfficeSupplies/GetOfficeSuppliesList',
  /** 统计 */
  GetOfficeSuppliesNumCount: '/OfficeSupplies/GetOfficeSuppliesNumCount',
  /** 获取办公用品分配首页列表 */
  GetOfficeSuppliesReceiveIndexList: '/OfficeSupplies/GetOfficeSuppliesReceiveIndexList',
  /** 获取办公用品接收列表 */
  GetOfficeSuppliesReceiveList: '/OfficeSupplies/GetOfficeSuppliesReceiveList',
  /** 获取办公用品单条 */
  GetOfficeSuppliesSingle: '/OfficeSupplies/GetOfficeSuppliesSingle',
  /** 归还办公用品 */
  GiveBackOfficeSupplies: '/OfficeSupplies/GiveBackOfficeSupplies',
  /** 办公用品提交审批 */
  OfficeSuppliesSubmit: '/OfficeSupplies/OfficeSuppliesSubmit',
  /** 接收办公用品 */
  ReceiveOfficeSupplies: '/OfficeSupplies/ReceiveOfficeSupplies',
  /** 办公用品审批不通过 */
  UnExamineOfficeSupplies: '/OfficeSupplies/UnExamineOfficeSupplies',
  /** 修改办公用品 */
  UpdateOfficeSupplies: '/OfficeSupplies/UpdateOfficeSupplies',
}
export const apiAutoOfficeSuppliesGood = {
  /** 获取办公用品物品清单列表 */
  GetOfficeSuppliesGoodList: '/OfficeSuppliesGood/GetOfficeSuppliesGoodList',
}
export const apiAutoOgCommissionConfig = {
  /** 新增提成配置管理 */
  CreateOgCommissionConfig: '/OgCommissionConfig/CreateOgCommissionConfig',
  /** 删除提成配置管理 */
  DeleteOgCommissionConfig: '/OgCommissionConfig/DeleteOgCommissionConfig',
  /** 获取提成配置管理列表 */
  GetOgCommissionConfig: '/OgCommissionConfig/GetOgCommissionConfig',
  /** 修改提成配置管理 */
  UpdateOgCommissionConfig: '/OgCommissionConfig/UpdateOgCommissionConfig',
}
export const apiAutoOperationManual = {
  /** 新增操作手册 */
  AddOperationManual: '/OperationManual/AddOperationManual',
  /** 删除操作手册 */
  DeleteOperationManual: '/OperationManual/DeleteOperationManual',
  /** 获取操作手册列表 */
  GetOperationManualList: '/OperationManual/GetOperationManualList',
  /** 修改操作手册 */
  UpdateOperationManual: '/OperationManual/UpdateOperationManual',
}
export const apiAutoOrganization = {
  /** 新增组织架构-公司部门 */
  AddOrganization: '/Organization/AddOrganization',
  /** 删除组织架构-公司部门 */
  DeleteOrganization: '/Organization/DeleteOrganization',
  /** 获取组织架构-公司部门 列表 */
  GetOrganizationList: '/Organization/GetOrganizationList',
  /** 获取组织架构-公司部门  BY DoId */
  GetOrganizationSingle: '/Organization/GetOrganizationSingle',
  /** 修改组织架构-公司部门 */
  UpdateOrganization: '/Organization/UpdateOrganization',
}
export const apiAutoOrganizationCert = {
  /** 新增单位证书 */
  AddOrganizationCert: '/OrganizationCert/AddOrganizationCert',
  /** 删除单位证书 */
  DeleteOrganizationCert: '/OrganizationCert/DeleteOrganizationCert',
  /** 获取单位证书列表 */
  GetOrganizationCertList: '/OrganizationCert/GetOrganizationCertList',
  /** 修改单位证书 */
  UpdateOrganizationCert: '/OrganizationCert/UpdateOrganizationCert',
}
export const apiAutoOutputValueDep = {
  /** 新增产值分配部门 */
  AddOutputValueDep: '/OutputValueDep/AddOutputValueDep',
  /** 删除产值分配部门 */
  DeleteOutputValueDep: '/OutputValueDep/DeleteOutputValueDep',
  /** 获取产值分配部门列表 */
  GetOutputValueDepList: '/OutputValueDep/GetOutputValueDepList',
  /** 修改产值分配部门 */
  UpdateOutputValueDep: '/OutputValueDep/UpdateOutputValueDep',
}
export const apiAutoOutputValueDepRedistribution = {
  /** 新增产值再分配部门 */
  AddOutputValueDepRedistribution: '/OutputValueDepRedistribution/AddOutputValueDepRedistribution',
  /** 删除产值再分配部门 */
  DeleteOutputValueDepRedistribution: '/OutputValueDepRedistribution/DeleteOutputValueDepRedistribution',
  /** 获取产值再分配部门列表 */
  GetOutputValueDepRedistributionList: '/OutputValueDepRedistribution/GetOutputValueDepRedistributionList',
  /** 修改产值再分配部门 */
  UpdateOutputValueDepRedistribution: '/OutputValueDepRedistribution/UpdateOutputValueDepRedistribution',
}
export const apiAutoOutputValuePer = {
  /** 新增产值分配人员 */
  AddOutputValuePer: '/OutputValuePer/AddOutputValuePer',
  /** 批量修改人员 */
  BatchUpdateOutputValuePerV2: '/OutputValuePer/BatchUpdateOutputValuePerV2',
  /** 批量添加人员 */
  BathAddOutputValuePer: '/OutputValuePer/BathAddOutputValuePer',
  /** 批量修改人员 */
  BathUpdateOutputValuePer: '/OutputValuePer/BathUpdateOutputValuePer',
  /** 删除产值分配人员 */
  DeleteOutputValuePer: '/OutputValuePer/DeleteOutputValuePer',
  /** 获取产值分配人员列表 */
  GetOutputValuePerList: '/OutputValuePer/GetOutputValuePerList',
  /** 提成数量统计 */
  GetVPerCommissionCount: '/OutputValuePer/GetVPerCommissionCount',
  /** 获取产值分配人员列表--提成 */
  GetVPerCommissionList: '/OutputValuePer/GetVPerCommissionList',
  /** 提成金额统计 */
  GetVPerCommissionPriceCount: '/OutputValuePer/GetVPerCommissionPriceCount',
  /** 修改产值分配人员 */
  UpdateOutputValuePer: '/OutputValuePer/UpdateOutputValuePer',
  /** 批量修改产值分配人员 */
  UpdateOutputValuePerBath: '/OutputValuePer/UpdateOutputValuePerBath',
}
export const apiAutoOutputValuePerRedistribution = {
  /** 新增产值分配人员 */
  AddOutputValuePerRedistribution: '/OutputValuePerRedistribution/AddOutputValuePerRedistribution',
  /** 删除产值分配人员 */
  DeleteOutputValuePerRedistribution: '/OutputValuePerRedistribution/DeleteOutputValuePerRedistribution',
  /** 获取产值分配人员列表 */
  GetOutputValuePerRedistributionList: '/OutputValuePerRedistribution/GetOutputValuePerRedistributionList',
  /** 修改产值分配人员 */
  UpdateOutputValuePerRedistribution: '/OutputValuePerRedistribution/UpdateOutputValuePerRedistribution',
}
export const apiAutoOverallTemplateManage = {
  /** 新增全局架构模板管理 */
  AddOverallTemplateManage: '/OverallTemplateManage/AddOverallTemplateManage',
  /** 复制全局架构模板管理 */
  CopyOverallTemplateManage: '/OverallTemplateManage/CopyOverallTemplateManage',
  /** 删除全局架构模板管理 */
  DeleteOverallTemplateManage: '/OverallTemplateManage/DeleteOverallTemplateManage',
  /** 获取全局架构模板管理列表 */
  GetOverallTemplateManageList: '/OverallTemplateManage/GetOverallTemplateManageList',
  /** 修改全局架构模板管理 */
  UpdateOverallTemplateManage: '/OverallTemplateManage/UpdateOverallTemplateManage',
}
export const apiAutoPageDivision = {
  /** 新增或修改结算审批界面划分 */
  AddOrUpdatePageDivision: '/PageDivision/AddOrUpdatePageDivision',
  /** 删除结算审批界面划分 */
  DeletePageDivision: '/PageDivision/DeletePageDivision',
  /** 获取结算审批界面划分列表 */
  GetPageDivisionList: '/PageDivision/GetPageDivisionList',
}
export const apiAutoPayroll = {
  /** 新增工资单 */
  AddPayroll: '/Payroll/AddPayroll',
  /** 删除工资单 */
  DeletePayroll: '/Payroll/DeletePayroll',
  /** 获取工资单列表 */
  GetPayrollList: '/Payroll/GetPayrollList',
  /** 统计工资单列表金额 */
  GetPayrollListSumMoney: '/Payroll/GetPayrollListSumMoney',
  /** 获取单条工资单 */
  GetPayrollSingle: '/Payroll/GetPayrollSingle',
  /** 修改工资单 */
  UpdatePayroll: '/Payroll/UpdatePayroll',
}
export const apiAutoPayrollFile = {
  /** 新增工资单文件 */
  AddPayrollFile: '/PayrollFile/AddPayrollFile',
  /** 删除工资单文件 */
  DeletePayrollFile: '/PayrollFile/DeletePayrollFile',
  /** 获取工资单文件列表 */
  GetPayrollFileList: '/PayrollFile/GetPayrollFileList',
  /** 修改工资单文件 */
  UpdatePayrollFile: '/PayrollFile/UpdatePayrollFile',
}
export const apiAutoPbaDeposit = {
  /** 新增保证金 */
  AddPbaDeposit: '/PbaDeposit/AddPbaDeposit',
  /** 批量退还 */
  BathPbaDepositBack: '/PbaDeposit/BathPbaDepositBack',
  /** 批量缴纳 */
  BathPbaDepositPaid: '/PbaDeposit/BathPbaDepositPaid',
  /** 删除保证金 */
  DeletePbaDeposit: '/PbaDeposit/DeletePbaDeposit',
  /** 导出招标代理保证金 */
  ExportPbaDeposit: '/PbaDeposit/ExportPbaDeposit',
  /** 获取保证金列表 */
  GetPbaDepositList: '/PbaDeposit/GetPbaDepositList',
  /** 获取保证金Count */
  GetPbaDepositListCount: '/PbaDeposit/GetPbaDepositListCount',
  /** 是否作废保证金 */
  IsCancelPbaDeposit: '/PbaDeposit/IsCancelPbaDeposit',
  /** 修改保证金 */
  UpdatePbaDeposit: '/PbaDeposit/UpdatePbaDeposit',
  /** 导入招标代理保证金 */
  UploadPbaDeposit: '/PbaDeposit/UploadPbaDeposit',
}
export const apiAutoPbaExpertFee = {
  /** 添加专家费 */
  AddPbaExpertFee: '/PbaExpertFee/AddPbaExpertFee',
  /** 作废 */
  CancelPbaExpertFee: '/PbaExpertFee/CancelPbaExpertFee',
  /** 删除专家费 */
  DeletePbaExpertFee: '/PbaExpertFee/DeletePbaExpertFee',
  /** 获取专家费 */
  GetPbaExpertFeeList: '/PbaExpertFee/GetPbaExpertFeeList',
  /** 更新专家费 */
  UpdatePbaExpertFee: '/PbaExpertFee/UpdatePbaExpertFee',
}
export const apiAutoPbaFile = {
  /** 新增项目文件 */
  AddPbaFile: '/PbaFile/AddPbaFile',
  /** 批量新增项目文件 */
  AddPbaFileBath: '/PbaFile/AddPbaFileBath',
  /** 删除项目文件 */
  DeletePbaFile: '/PbaFile/DeletePbaFile',
  /** 获取项目文件列表 */
  GetPbaFileList: '/PbaFile/GetPbaFileList',
  /** 修改项目文件 */
  UpdatePbaFile: '/PbaFile/UpdatePbaFile',
}
export const apiAutoPbaFileConfig = {
  /** 新增招标代理文件配置 */
  AddPbaFileConfig: '/PbaFileConfig/AddPbaFileConfig',
  /** 批量新增招标代理文件配置 */
  BathAddPbaFileConfig: '/PbaFileConfig/BathAddPbaFileConfig',
  /** 删除招标代理文件配置 */
  DeletePbaFileConfig: '/PbaFileConfig/DeletePbaFileConfig',
  /** 获取招标代理文件配置列表 */
  GetPbaFileConfigList: '/PbaFileConfig/GetPbaFileConfigList',
  /** 获取招标代理文件配置单条 */
  GetPbaFileConfigSingle: '/PbaFileConfig/GetPbaFileConfigSingle',
  /** 修改招标代理文件配置 */
  UpdatePbaFileConfig: '/PbaFileConfig/UpdatePbaFileConfig',
}
export const apiAutoPbaFileFee = {
  /** 新增资料费 */
  AddPbaFileFee: '/PbaFileFee/AddPbaFileFee',
  /** 删除资料费 */
  DeletePbaFileFee: '/PbaFileFee/DeletePbaFileFee',
  /** 导出招标代理资料费 */
  ExportPbaFileFee: '/PbaFileFee/ExportPbaFileFee',
  /** 获取资料费列表 */
  GetPbaFileFeeList: '/PbaFileFee/GetPbaFileFeeList',
  /** 是否作废资料费 */
  IsCancelPbaFileFee: '/PbaFileFee/IsCancelPbaFileFee',
  /** 修改资料费 */
  UpdatePbaFileFee: '/PbaFileFee/UpdatePbaFileFee',
  /** 导入招标代理保证金 */
  UploadPbaFileFee: '/PbaFileFee/UploadPbaFileFee',
}
export const apiAutoPbaFileMeans = {
  /** 新增项目资料 */
  AddPbaFileMeans: '/PbaFileMeans/AddPbaFileMeans',
  /** 批量新增项目资料 */
  AddPbaFileMeansBath: '/PbaFileMeans/AddPbaFileMeansBath',
  /** 删除项目资料 */
  DeletePbaFileMeans: '/PbaFileMeans/DeletePbaFileMeans',
  /** 获取项目资料列表 */
  GetPbaFileMeansList: '/PbaFileMeans/GetPbaFileMeansList',
  /** 修改项目资料 */
  UpdatePbaFileMeans: '/PbaFileMeans/UpdatePbaFileMeans',
}
export const apiAutoPbaFileType = {
  /** 新增招标代理项目文件类型 */
  AddPbaFileType: '/PbaFileType/AddPbaFileType',
  /** 删除招标代理项目文件类型 */
  DeletePbaFileType: '/PbaFileType/DeletePbaFileType',
  /** 获取招标代理项目文件类型列表 */
  GetPbaFileTypeList: '/PbaFileType/GetPbaFileTypeList',
  /** 修改招标代理项目文件类型 */
  UpdatePbaFileType: '/PbaFileType/UpdatePbaFileType',
}
export const apiAutoPbaQuestion = {
  /** 新增招标代理问题 */
  AddPbaQuestion: '/PbaQuestion/AddPbaQuestion',
  /** 新增招标代理问题--批量 */
  AddPbaQuestionBath: '/PbaQuestion/AddPbaQuestionBath',
  /** 删除招标代理问题 */
  DeletePbaQuestion: '/PbaQuestion/DeletePbaQuestion',
  /** 获取招标代理问题列表 */
  GetPbaQuestionList: '/PbaQuestion/GetPbaQuestionList',
  /** 修改招标代理问题 */
  UpdatePbaQuestion: '/PbaQuestion/UpdatePbaQuestion',
}
export const apiAutoPbaRemark = {
  /** 新增招标代理备注 */
  AddPbaRemark: '/PbaRemark/AddPbaRemark',
  /** 删除招标代理备注 */
  DeletePbaRemark: '/PbaRemark/DeletePbaRemark',
  /** 获取招标代理备注列表 */
  GetPbaRemarkList: '/PbaRemark/GetPbaRemarkList',
}
export const apiAutoPbaUploadDocument = {
  /** 新增全局上传文档 */
  AddPbaUploadDocument: '/PbaUploadDocument/AddPbaUploadDocument',
  /** 删除全局上传文档 */
  DeletePbaUploadDocument: '/PbaUploadDocument/DeletePbaUploadDocument',
  /** 获取全局上传文档列表 */
  GetPbaUploadDocumentList: '/PbaUploadDocument/GetPbaUploadDocumentList',
  /** 修改全局上传文档 */
  UpdatePbaUploadDocument: '/PbaUploadDocument/UpdatePbaUploadDocument',
  /** 修改排序 */
  UpdatePbaUploadDocumentSort: '/PbaUploadDocument/UpdatePbaUploadDocumentSort',
}
export const apiAutoPboContractData = {
  /** 新增合同业绩 */
  AddPboContractData: '/PboContractData/AddPboContractData',
  /** 批量新增合同业绩 */
  AddPboContractDataBath: '/PboContractData/AddPboContractDataBath',
  /** 删除合同业绩 */
  DeletePboContractData: '/PboContractData/DeletePboContractData',
  /** 获取合同业绩列表ids */
  GetPboContractDataIdsList: '/PboContractData/GetPboContractDataIdsList',
  /** 获取合同业绩列表 */
  GetPboContractDataList: '/PboContractData/GetPboContractDataList',
  /** 修改合同业绩 */
  UpdatePboContractData: '/PboContractData/UpdatePboContractData',
}
export const apiAutoPboFile = {
  /** 新增投标文件 */
  AddPboFile: '/PboFile/AddPboFile',
  /** 批量新增投标文件 */
  AddPboFileBath: '/PboFile/AddPboFileBath',
  /** 删除投标文件 */
  DeletePboFile: '/PboFile/DeletePboFile',
  /** 获取投标文件列表 */
  GetPboFileList: '/PboFile/GetPboFileList',
  /** 是否存在指定类型的文件 */
  IsExistsPboFile: '/PboFile/IsExistsPboFile',
  /** 修改投标文件 */
  UpdatePboFile: '/PboFile/UpdatePboFile',
}
export const apiAutoPboFileConfig = {
  /** 新增投标作业文件配置 */
  AddPboFileConfig: '/PboFileConfig/AddPboFileConfig',
  /** 新增投标作业文件配置 */
  BathAddPboFileConfig: '/PboFileConfig/BathAddPboFileConfig',
  /** 删除投标作业文件配置 */
  DeletePboFileConfig: '/PboFileConfig/DeletePboFileConfig',
  /** 获取投标作业文件配置列表 */
  GetPboFileConfigList: '/PboFileConfig/GetPboFileConfigList',
  /** 修改投标作业文件配置 */
  UpdatePboFileConfig: '/PboFileConfig/UpdatePboFileConfig',
}
export const apiAutoPboFileMeans = {
  /** 新增项目资料 */
  AddPboFileMeans: '/PboFileMeans/AddPboFileMeans',
  /** 批量新增项目资料 */
  AddPboFileMeansBath: '/PboFileMeans/AddPboFileMeansBath',
  /** 删除项目资料 */
  DeletePboFileMeans: '/PboFileMeans/DeletePboFileMeans',
  /** 获取项目资料列表 */
  GetPboFileMeansList: '/PboFileMeans/GetPboFileMeansList',
  /** 修改项目资料 */
  UpdatePboFileMeans: '/PboFileMeans/UpdatePboFileMeans',
}
export const apiAutoPboFilePurchase = {
  /** 新增招标文件购买 */
  AddPboFilePurchase: '/PboFilePurchase/AddPboFilePurchase',
  /** 删除招标文件购买 */
  DeletePboFilePurchase: '/PboFilePurchase/DeletePboFilePurchase',
  /** 获取招标文件购买列表 */
  GetPboFilePurchaseList: '/PboFilePurchase/GetPboFilePurchaseList',
  /** 修改招标文件购买 */
  UpdatePboFilePurchase: '/PboFilePurchase/UpdatePboFilePurchase',
}
export const apiAutoPboFileType = {
  /** 新增投标项目文件类型 */
  AddPboFileType: '/PboFileType/AddPboFileType',
  /** 删除投标项目文件类型 */
  DeletePboFileType: '/PboFileType/DeletePboFileType',
  /** 获取投标项目文件类型列表 */
  GetPboFileTypeList: '/PboFileType/GetPboFileTypeList',
  /** 修改投标项目文件类型 */
  UpdatePboFileType: '/PboFileType/UpdatePboFileType',
}
export const apiAutoPboMajorData = {
  /** 新增专业人员 */
  AddPboMajorData: '/PboMajorData/AddPboMajorData',
  /** 批量新增专业人员 */
  AddPboMajorDataBath: '/PboMajorData/AddPboMajorDataBath',
  /** 删除专业人员 */
  DeletePboMajorData: '/PboMajorData/DeletePboMajorData',
  /** 获取专业人员列表ids */
  GetPboMajorDataIdsList: '/PboMajorData/GetPboMajorDataIdsList',
  /** 获取专业人员列表 */
  GetPboMajorDataList: '/PboMajorData/GetPboMajorDataList',
  /** 修改专业人员 */
  UpdatePboMajorData: '/PboMajorData/UpdatePboMajorData',
}
export const apiAutoPboOrganizationData = {
  /** 新增企业资料 */
  AddPboOrganizationData: '/PboOrganizationData/AddPboOrganizationData',
  /** 批量新增企业资料 */
  AddPboOrganizationDataBath: '/PboOrganizationData/AddPboOrganizationDataBath',
  /** 删除企业资料 */
  DeletePboOrganizationData: '/PboOrganizationData/DeletePboOrganizationData',
  /** 获取企业资料列表ids */
  GetPboOrganizationDataIdsList: '/PboOrganizationData/GetPboOrganizationDataIdsList',
  /** 获取企业资料列表 */
  GetPboOrganizationDataList: '/PboOrganizationData/GetPboOrganizationDataList',
  /** 修改企业资料 */
  UpdatePboOrganizationData: '/PboOrganizationData/UpdatePboOrganizationData',
}
export const apiAutoPboQuestion = {
  /** 新增投标项目问题 */
  AddPboQuestion: '/PboQuestion/AddPboQuestion',
  /** 新增投标项目问题--批量 */
  AddPboQuestionBath: '/PboQuestion/AddPboQuestionBath',
  /** 删除投标项目问题 */
  DeletePboQuestion: '/PboQuestion/DeletePboQuestion',
  /** 获取投标项目问题列表 */
  GetPboQuestionList: '/PboQuestion/GetPboQuestionList',
  /** 修改投标项目问题 */
  UpdatePboQuestion: '/PboQuestion/UpdatePboQuestion',
}
export const apiAutoPboRemark = {
  /** 新增投标项目备注 */
  AddPboRemark: '/PboRemark/AddPboRemark',
  /** 删除投标项目备注 */
  DeletePboRemark: '/PboRemark/DeletePboRemark',
  /** 获取投标项目备注列表 */
  GetPboRemarkList: '/PboRemark/GetPboRemarkList',
}
export const apiAutoPboReportPerformance = {
  /** 新增报告业绩 */
  AddPboReportPerformance: '/PboReportPerformance/AddPboReportPerformance',
  /** 批量新增报告业绩 */
  AddPboReportPerformanceBath: '/PboReportPerformance/AddPboReportPerformanceBath',
  /** 删除报告业绩 */
  DeletePboReportPerformance: '/PboReportPerformance/DeletePboReportPerformance',
  /** 获取报告业绩列表ids */
  GetPboReportPerformanceIdsList: '/PboReportPerformance/GetPboReportPerformanceIdsList',
  /** 获取报告业绩列表 */
  GetPboReportPerformanceList: '/PboReportPerformance/GetPboReportPerformanceList',
  /** 修改报告业绩 */
  UpdatePboReportPerformance: '/PboReportPerformance/UpdatePboReportPerformance',
}
export const apiAutoPboUploadDocument = {
  /** 新增投标咨询文件 */
  AddPboUploadDocument: '/PboUploadDocument/AddPboUploadDocument',
  /** 删除投标咨询文件 */
  DeletePboUploadDocument: '/PboUploadDocument/DeletePboUploadDocument',
  /** 获取投标咨询文件列表 */
  GetPboUploadDocumentList: '/PboUploadDocument/GetPboUploadDocumentList',
  /** 修改投标咨询文件 */
  UpdatePboUploadDocument: '/PboUploadDocument/UpdatePboUploadDocument',
}
export const apiAutoPboZxTypeConfig = {
  /** 新增投标咨询类型配置 */
  AddPboZxTypeConfig: '/PboZxTypeConfig/AddPboZxTypeConfig',
  /** 删除投标咨询类型配置 */
  DeletePboZxTypeConfig: '/PboZxTypeConfig/DeletePboZxTypeConfig',
  /** 获取投标咨询类型配置列表 */
  GetPboZxTypeConfigList: '/PboZxTypeConfig/GetPboZxTypeConfigList',
  /** 修改投标咨询类型配置 */
  UpdatePboZxTypeConfig: '/PboZxTypeConfig/UpdatePboZxTypeConfig',
}
export const apiAutoPdf = {
  /**  */
  Combine: '/Pdf/Combine',
  /**  */
  ExcelRowToCell: '/Pdf/ExcelRowToCell',
}
export const apiAutoPerAddressBook = {
  /** 新增个人通讯录 */
  AddPerAddressBook: '/PerAddressBook/AddPerAddressBook',
  /** 复制个人通讯录 */
  CopyPerAddressBook: '/PerAddressBook/CopyPerAddressBook',
  /** 删除个人通讯录 */
  DeletePerAddressBook: '/PerAddressBook/DeletePerAddressBook',
  /** 获取个人通讯录列表 */
  GetPerAddressBookList: '/PerAddressBook/GetPerAddressBookList',
  /** 修改个人通讯录 */
  UpdatePerAddressBook: '/PerAddressBook/UpdatePerAddressBook',
}
export const apiAutoPerformanceManagement = {
  /** 新增绩效管理 */
  AddPerformanceManagement: '/PerformanceManagement/AddPerformanceManagement',
  /** 作废绩效管理 */
  CancelPerformanceManagement: '/PerformanceManagement/CancelPerformanceManagement',
  /** 删除绩效管理 */
  DeletePerformanceManagement: '/PerformanceManagement/DeletePerformanceManagement',
  /** 审批通过 */
  ExaminePerformanceManagement: '/PerformanceManagement/ExaminePerformanceManagement',
  /** 获取绩效管理Excel */
  GetPerformanceManagementExcel: '/PerformanceManagement/GetPerformanceManagementExcel',
  /** 获取绩效管理列表 */
  GetPerformanceManagementList: '/PerformanceManagement/GetPerformanceManagementList',
  /** 获取绩效管理列表Count */
  GetPerformanceManagementListCount: '/PerformanceManagement/GetPerformanceManagementListCount',
  /** 获取绩效管理列表底部统计 */
  GetPerformanceManagementListCountMoney: '/PerformanceManagement/GetPerformanceManagementListCountMoney',
  /** 获取绩效管理单条 */
  GetPerformanceManagementSingle: '/PerformanceManagement/GetPerformanceManagementSingle',
  /** 分配开票 */
  PmInvoiceAllocation: '/PerformanceManagement/PmInvoiceAllocation',
  /** 提交审批 */
  SubmitPerformanceManagement: '/PerformanceManagement/SubmitPerformanceManagement',
  /** 审批不通过 */
  UnExaminePerformanceManagement: '/PerformanceManagement/UnExaminePerformanceManagement',
  /** 修改绩效管理 */
  UpdatePerformanceManagement: '/PerformanceManagement/UpdatePerformanceManagement',
  /** 修改项目分配状态 */
  UpdateProJxFpState: '/PerformanceManagement/UpdateProJxFpState',
}
export const apiAutoPinMingApi = {
  /** 激活归档项目接口 */
  ActiveAchieveProject: '/PinMingApi/ActiveAchieveProject',
  /** 获取合稿文件 */
  GetPinmingProFileSingle: '/PinMingApi/GetPinmingProFileSingle',
  /** 获取本系统暂存品茗项目 */
  GetPinmingProjectSingle: '/PinMingApi/GetPinmingProjectSingle',
  /** 获取典型案例分类接口 */
  GetTypicalProjectCategoryTree: '/PinMingApi/GetTypicalProjectCategoryTree',
  /** 获取典型案例分类接口 */
  GetTypicalProjectCategoryTree1: '/PinMingApi/GetTypicalProjectCategoryTree1',
  /** 通知归档接口 */
  NotifyAchieveProject: '/PinMingApi/NotifyAchieveProject',
  /** 逻辑删除项目 */
  NotifyLogicalDelProject: '/PinMingApi/NotifyLogicalDelProject',
  /** 品茗登录 */
  PmLogin: '/PinMingApi/PmLogin',
  /** 上传造价文件 */
  PmUploadFile: '/PinMingApi/PmUploadFile',
  /** 推送项目 */
  PushProject: '/PinMingApi/PushProject',
  /** 本系统暂存品茗项目 */
  SavePinmingProject: '/PinMingApi/SavePinmingProject',
  /** 获取典型案例信息接口 */
  SearchTypicalProjectByPage: '/PinMingApi/SearchTypicalProjectByPage',
  /** 品茗新建项目 */
  addProject: '/PinMingApi/addProject',
  /** 增加项目成员 */
  addProjectUserList: '/PinMingApi/addProjectUserList',
  /** 批量删除项目 */
  batchDeleteProject: '/PinMingApi/batchDeleteProject',
  /** 绑定企业 */
  bindEnterprise: '/PinMingApi/bindEnterprise',
  /** 在线浏览造价文件 */
  browserFile: '/PinMingApi/browserFile',
  /** 删除项目成员 */
  deleteProjectUserList: '/PinMingApi/deleteProjectUserList',
  /** 获取文件解析状态 */
  getAnalysisStatus: '/PinMingApi/getAnalysisStatus',
  /** 查询企业用户列表 */
  getEmployeeList: '/PinMingApi/getEmployeeList',
  /** 打开胜算工程文件 */
  getOpenSsProject: '/PinMingApi/getOpenSsProject',
  /** 查询项目专业分类接口 */
  getProjectCategory: '/PinMingApi/getProjectCategory',
  /** 获取项目文件地址 */
  getProjectFileUrl: '/PinMingApi/getProjectFileUrl',
  /** 查询项目日志接口 */
  getProjectLog: '/PinMingApi/getProjectLog',
  /** 查询项目类型接口 */
  getProjectType: '/PinMingApi/getProjectType',
  /** 获取省市县 */
  getRegionTree: '/PinMingApi/getRegionTree',
  /** 查看项目详情 */
  projectDetail: '/PinMingApi/projectDetail',
  /** 刷新token */
  refresh: '/PinMingApi/refresh',
  /** 替换项目成员 */
  replaceProjectUser: '/PinMingApi/replaceProjectUser',
  /** 查询项目列表 */
  searchProjectPage: '/PinMingApi/searchProjectPage',
  /** 修改项目锁定状态 */
  updateLockStatus: '/PinMingApi/updateLockStatus',
  /** 品茗修改项目 */
  updateProject: '/PinMingApi/updateProject',
}
export const apiAutoPmAdvancePayment = {
  /** 新增预付款款项模板 */
  AddPmAdvancePayment: '/PmAdvancePayment/AddPmAdvancePayment',
  /** 删除预付款款项模板 */
  DeletePmAdvancePayment: '/PmAdvancePayment/DeletePmAdvancePayment',
  /** 获取预付款款项模板列表 */
  GetPmAdvancePaymentList: '/PmAdvancePayment/GetPmAdvancePaymentList',
  /** 修改预付款款项模板 */
  UpdatePmAdvancePayment: '/PmAdvancePayment/UpdatePmAdvancePayment',
}
export const apiAutoPmAdvancePaymentField = {
  /** 新增预付款款项模板字段 */
  AddPmAdvancePaymentField: '/PmAdvancePaymentField/AddPmAdvancePaymentField',
  /** 删除预付款款项模板字段 */
  DeletePmAdvancePaymentField: '/PmAdvancePaymentField/DeletePmAdvancePaymentField',
  /** 获取预付款款项模板字段列表 */
  GetPmAdvancePaymentFieldList: '/PmAdvancePaymentField/GetPmAdvancePaymentFieldList',
  /** 修改预付款款项模板字段 */
  UpdatePmAdvancePaymentField: '/PmAdvancePaymentField/UpdatePmAdvancePaymentField',
}
export const apiAutoPmAdvancePaymentWithin = {
  /** 新增项目内预付款款项 */
  AddPmAdvancePaymentWithin: '/PmAdvancePaymentWithin/AddPmAdvancePaymentWithin',
  /** 删除项目内预付款款项 */
  DeletePmAdvancePaymentWithin: '/PmAdvancePaymentWithin/DeletePmAdvancePaymentWithin',
  /** 获取项目内预付款款项列表 */
  GetPmAdvancePaymentWithinList: '/PmAdvancePaymentWithin/GetPmAdvancePaymentWithinList',
  /** 是否启用 */
  IsOpenPmAdvancePaymentWithin: '/PmAdvancePaymentWithin/IsOpenPmAdvancePaymentWithin',
  /** 修改项目内预付款款项 */
  UpdatePmAdvancePaymentWithin: '/PmAdvancePaymentWithin/UpdatePmAdvancePaymentWithin',
}
export const apiAutoPmAgreementRejection = {
  /** 新增全过程标段合同甩项金额 */
  AddPmAgreementRejection: '/PmAgreementRejection/AddPmAgreementRejection',
  /** 删除全过程标段合同甩项金额 */
  DeletePmAgreementRejection: '/PmAgreementRejection/DeletePmAgreementRejection',
  /** 获取全过程标段合同甩项金额列表 */
  GetPmAgreementRejectionList: '/PmAgreementRejection/GetPmAgreementRejectionList',
  /** 修改全过程标段合同甩项金额 */
  UpdatePmAgreementRejection: '/PmAgreementRejection/UpdatePmAgreementRejection',
}
export const apiAutoPmCertificate = {
  /** 新增项管证书 */
  AddPmCertificate: '/PmCertificate/AddPmCertificate',
  /** 删除项管证书 */
  DeletePmCertificate: '/PmCertificate/DeletePmCertificate',
  /** 获取项管证书首页代办列表 */
  GetPmCertificateIndexToDoList: '/PmCertificate/GetPmCertificateIndexToDoList',
  /** 获取项管证书列表 */
  GetPmCertificateList: '/PmCertificate/GetPmCertificateList',
  /** 获取项管证书列表统计 */
  GetPmCertificateListCount: '/PmCertificate/GetPmCertificateListCount',
  /** 修改项管证书 */
  UpdatePmCertificate: '/PmCertificate/UpdatePmCertificate',
}
export const apiAutoPmCertificateType = {
  /** 新增项管证书类型 */
  AddPmCertificateType: '/PmCertificateType/AddPmCertificateType',
  /** 删除项管证书类型 */
  DeletePmCertificateType: '/PmCertificateType/DeletePmCertificateType',
  /** 获取项管证书类型列表 */
  GetPmCertificateTypeList: '/PmCertificateType/GetPmCertificateTypeList',
  /** 修改项管证书类型 */
  UpdatePmCertificateType: '/PmCertificateType/UpdatePmCertificateType',
}
export const apiAutoPmCoordinateMeeting = {
  /** 新增协调会议信息 */
  AddPmCoordinateMeeting: '/PmCoordinateMeeting/AddPmCoordinateMeeting',
  /** 作废协调会议信息 */
  CancelPmCoordinateMeeting: '/PmCoordinateMeeting/CancelPmCoordinateMeeting',
  /** 删除协调会议信息 */
  DeletePmCoordinateMeeting: '/PmCoordinateMeeting/DeletePmCoordinateMeeting',
  /** 获取协调会议信息 */
  GetPmCoordinateMeetingList: '/PmCoordinateMeeting/GetPmCoordinateMeetingList',
  /** 获取协调会议信息 */
  GetPmCoordinateMeetingSingle: '/PmCoordinateMeeting/GetPmCoordinateMeetingSingle',
  /** 修改协调会议信息 */
  UpdatePmCoordinateMeeting: '/PmCoordinateMeeting/UpdatePmCoordinateMeeting',
}
export const apiAutoPmDynamicCost = {
  /** 新增全过程咨询动态造价 */
  AddPmDynamicCost: '/PmDynamicCost/AddPmDynamicCost',
  /** 作废全过程咨询动态造价 */
  CancelPmDynamicCost: '/PmDynamicCost/CancelPmDynamicCost',
  /** 删除全过程咨询动态造价 */
  DeletePmDynamicCost: '/PmDynamicCost/DeletePmDynamicCost',
  /** 获取应增加列表 */
  GetPmDynamicAddChargeList: '/PmDynamicCost/GetPmDynamicAddChargeList',
  /** 获取预付款列表 */
  GetPmDynamicAdvanceChargeList: '/PmDynamicCost/GetPmDynamicAdvanceChargeList',
  /** 项目进度款当前金额分析 */
  GetPmDynamicCostCurMoneyAnalysis: '/PmDynamicCost/GetPmDynamicCostCurMoneyAnalysis',
  /** 获取全过程咨询动态造价列表 */
  GetPmDynamicCostList: '/PmDynamicCost/GetPmDynamicCostList',
  /** 项目进度款累计金额分析 */
  GetPmDynamicCostLjMoneyAnalysis: '/PmDynamicCost/GetPmDynamicCostLjMoneyAnalysis',
  /** 获取动态造价金统计 */
  GetPmDynamicCostMoneyCount: '/PmDynamicCost/GetPmDynamicCostMoneyCount',
  /** 获取全过程咨询动态造价--单条 */
  GetPmDynamicCostSingle: '/PmDynamicCost/GetPmDynamicCostSingle',
  /** 获取动态造价类型Count */
  GetPmDynamicCostTypeCount: '/PmDynamicCost/GetPmDynamicCostTypeCount',
  /** 获取应抵扣列表 */
  GetPmDynamicDeductionChargeList: '/PmDynamicCost/GetPmDynamicDeductionChargeList',
  /** 获取工程记录预付款列表 */
  GetPmDynamicGcAdvanceChargeList: '/PmDynamicCost/GetPmDynamicGcAdvanceChargeList',
  /** 获取工程记录列表 */
  GetPmDynamicGcRecordList: '/PmDynamicCost/GetPmDynamicGcRecordList',
  /** 资金计划按月统计--副屏分析 */
  GetPmFundPlanCountAnalysis: '/PmDynamicCost/GetPmFundPlanCountAnalysis',
  /** 项目产值分析 */
  GetPmOutputProgressAnalysis: '/PmDynamicCost/GetPmOutputProgressAnalysis',
  /** 项目五算分析 */
  GetPmProjectFiveCalculateAnalysis: '/PmDynamicCost/GetPmProjectFiveCalculateAnalysis',
  /** 项目审定金额分析 */
  GetPmSdMoneyAnalysis: '/PmDynamicCost/GetPmSdMoneyAnalysis',
  /** 获取合同价款和累计支付 */
  GetReHtPayJdkTjList: '/PmDynamicCost/GetReHtPayJdkTjList',
  /** 金额变更明细统计 */
  MoneyUpdateCount: '/PmDynamicCost/MoneyUpdateCount',
  /** 预付款统计 */
  PmDynamicDeductionChargeCount: '/PmDynamicCost/PmDynamicDeductionChargeCount',
  /** 资金计划统计 */
  PmFundPlanCount: '/PmDynamicCost/PmFundPlanCount',
  /** 进度款统计 */
  PmProgressPaymentCount: '/PmDynamicCost/PmProgressPaymentCount',
  /** 工期进度明细统计 */
  PmProgressScheduleCount: '/PmDynamicCost/PmProgressScheduleCount',
  /** 总表统计 */
  PmSummaryStatementCount: '/PmDynamicCost/PmSummaryStatementCount',
  /** 修改全过程咨询动态造价 */
  UpdatePmDynamicCost: '/PmDynamicCost/UpdatePmDynamicCost',
}
export const apiAutoPmDynamicGcConfig = {
  /** 新增动态造价工程配置 */
  AddPmDynamicGcConfig: '/PmDynamicGcConfig/AddPmDynamicGcConfig',
  /** 删除动态造价工程配置 */
  DeletePmDynamicGcConfig: '/PmDynamicGcConfig/DeletePmDynamicGcConfig',
  /** 获取动态造价工程配置列表 */
  GetPmDynamicGcConfigList: '/PmDynamicGcConfig/GetPmDynamicGcConfigList',
  /** 获取动态造价工程配置单条 */
  GetPmDynamicGcConfigSingle: '/PmDynamicGcConfig/GetPmDynamicGcConfigSingle',
  /** 修改动态造价工程配置 */
  UpdatePmDynamicGcConfig: '/PmDynamicGcConfig/UpdatePmDynamicGcConfig',
}
export const apiAutoPmGroupTemplate = {
  /** 新增项目组织模板 */
  AddPmGroupTemplate: '/PmGroupTemplate/AddPmGroupTemplate',
  /** 删除项目组织模板 */
  DeletePmGroupTemplate: '/PmGroupTemplate/DeletePmGroupTemplate',
  /** 获取项目组织模板列表 */
  GetPmGroupTemplateList: '/PmGroupTemplate/GetPmGroupTemplateList',
  /** 修改项目组织模板 */
  UpdatePmGroupTemplate: '/PmGroupTemplate/UpdatePmGroupTemplate',
}
export const apiAutoPmGroupTemplateField = {
  /** 新增项目组织模板字段 */
  AddPmGroupTemplateField: '/PmGroupTemplateField/AddPmGroupTemplateField',
  /** 删除项目组织模板字段 */
  DeletePmGroupTemplateField: '/PmGroupTemplateField/DeletePmGroupTemplateField',
  /** 获取项目组织模板字段列表 */
  GetPmGroupTemplateFieldList: '/PmGroupTemplateField/GetPmGroupTemplateFieldList',
  /** 修改项目组织模板字段 */
  UpdatePmGroupTemplateField: '/PmGroupTemplateField/UpdatePmGroupTemplateField',
}
export const apiAutoPmProgressConfig = {
  /** 新增进度款款项新表 */
  AddPmProgressConfig: '/PmProgressConfig/AddPmProgressConfig',
  /** 删除进度款款项新表 */
  DeletePmProgressConfig: '/PmProgressConfig/DeletePmProgressConfig',
  /** 获取进度款款项新表列表 */
  GetPmProgressConfigList: '/PmProgressConfig/GetPmProgressConfigList',
  /** 修改进度款款项新表 */
  UpdatePmProgressConfig: '/PmProgressConfig/UpdatePmProgressConfig',
}
export const apiAutoPmProgressTemplate = {
  /** 新增进度款款项模板 */
  AddPmProgressTemplate: '/PmProgressTemplate/AddPmProgressTemplate',
  /** 删除进度款款项模板 */
  DeletePmProgressTemplate: '/PmProgressTemplate/DeletePmProgressTemplate',
  /** 获取进度款款项模板列表 */
  GetPmProgressTemplateList: '/PmProgressTemplate/GetPmProgressTemplateList',
  /** 修改进度款款项模板 */
  UpdatePmProgressTemplate: '/PmProgressTemplate/UpdatePmProgressTemplate',
}
export const apiAutoPmProgressTemplateField = {
  /** 新增进度款款项模板字段 */
  AddPmProgressTemplateField: '/PmProgressTemplateField/AddPmProgressTemplateField',
  /** 删除进度款款项模板字段 */
  DeletePmProgressTemplateField: '/PmProgressTemplateField/DeletePmProgressTemplateField',
  /** 获取进度款款项模板字段列表 */
  GetPmProgressTemplateFieldList: '/PmProgressTemplateField/GetPmProgressTemplateFieldList',
  /** 修改进度款款项模板字段 */
  UpdatePmProgressTemplateField: '/PmProgressTemplateField/UpdatePmProgressTemplateField',
}
export const apiAutoPmReconciliationRecord = {
  /** 新增对账记录信息 */
  AddPmReconciliationRecord: '/PmReconciliationRecord/AddPmReconciliationRecord',
  /** 作废对账记录信息 */
  CancelPmReconciliationRecord: '/PmReconciliationRecord/CancelPmReconciliationRecord',
  /** 删除对账记录信息 */
  DeletePmReconciliationRecord: '/PmReconciliationRecord/DeletePmReconciliationRecord',
  /** 获取对账记录信息 */
  GetPmReconciliationRecordList: '/PmReconciliationRecord/GetPmReconciliationRecordList',
  /** 获取对账记录信息 */
  GetPmReconciliationRecordSingle: '/PmReconciliationRecord/GetPmReconciliationRecordSingle',
  /** 修改对账记录信息 */
  UpdatePmReconciliationRecord: '/PmReconciliationRecord/UpdatePmReconciliationRecord',
}
export const apiAutoProAssistPer = {
  /** 获取项目协管人列表 */
  GetProAssistPerList: '/ProAssistPer/GetProAssistPerList',
}
export const apiAutoProBaseInfo = {
  /** 获取项目结算指标基本信息列表 */
  GetProBaseInfoSingle: '/ProBaseInfo/GetProBaseInfoSingle',
  /** 修改模型 */
  UpdateCostTemplate: '/ProBaseInfo/UpdateCostTemplate',
  /** 修改模型--清空树结构 */
  UpdateCostTemplateAndDelTree: '/ProBaseInfo/UpdateCostTemplateAndDelTree',
  /** 修改项目结算指标基本信息 */
  UpdateProBaseInfo: '/ProBaseInfo/UpdateProBaseInfo',
}
export const apiAutoProBaseInfoCase = {
  /** 获取项目案例基本信息列表 */
  GetProBaseInfoCaseSingle: '/ProBaseInfoCase/GetProBaseInfoCaseSingle',
  /** 修改模型 */
  UpdateCostTemplate: '/ProBaseInfoCase/UpdateCostTemplate',
  /** 修改模型--清空树结构 */
  UpdateCostTemplateAndDelTree: '/ProBaseInfoCase/UpdateCostTemplateAndDelTree',
  /** 修改项目案例基本信息 */
  UpdateProBaseInfoCase: '/ProBaseInfoCase/UpdateProBaseInfoCase',
}
export const apiAutoProBiddingAgency = {
  /** 新增招标代理项目 */
  AddProBiddingAgency: '/ProBiddingAgency/AddProBiddingAgency',
  /** 项目存档回档 */
  BackProArchive: '/ProBiddingAgency/BackProArchive',
  /** 作废招标代理项目 */
  CancelProBiddingAgency: '/ProBiddingAgency/CancelProBiddingAgency',
  /** 互检通过 */
  CrossCheck: '/ProBiddingAgency/CrossCheck',
  /** 互检打回 */
  CrossCheckBack: '/ProBiddingAgency/CrossCheckBack',
  /** 删除招标代理项目 */
  DeleteProBiddingAgency: '/ProBiddingAgency/DeleteProBiddingAgency',
  /** 部门复核通过 */
  DepReview: '/ProBiddingAgency/DepReview',
  /** 部门复核打回 */
  DepReviewBack: '/ProBiddingAgency/DepReviewBack',
  /** 部门复核结束 */
  DepReviewEnd: '/ProBiddingAgency/DepReviewEnd',
  /** 招标代理项目审批通过 */
  ExamineProBiddingAgency: '/ProBiddingAgency/ExamineProBiddingAgency',
  /** 自定义地址招标代理统计导出 */
  ExportCusAddressProBiddingAgency: '/ProBiddingAgency/ExportCusAddressProBiddingAgency',
  /** 招标代理项目导出 */
  ExportProBiddingAgency: '/ProBiddingAgency/ExportProBiddingAgency',
  /** 获取招标代理项目列表 */
  GetProBiddingAgencyIndexList: '/ProBiddingAgency/GetProBiddingAgencyIndexList',
  /** 获取招标代理项目列表 */
  GetProBiddingAgencyList: '/ProBiddingAgency/GetProBiddingAgencyList',
  /** 获取招标代理项目列表统计 */
  GetProBiddingAgencyListCount: '/ProBiddingAgency/GetProBiddingAgencyListCount',
  /** 获取招标代理项目互检列表 */
  GetProBiddingAgencyReCheckList: '/ProBiddingAgency/GetProBiddingAgencyReCheckList',
  /** 获取招标代理项目复核列表 */
  GetProBiddingAgencyReviewList: '/ProBiddingAgency/GetProBiddingAgencyReviewList',
  /** 获取招标代理项目--单条 */
  GetProBiddingAgencySingle: '/ProBiddingAgency/GetProBiddingAgencySingle',
  /** 获取招标代理项目综合统计 */
  GetProbiddingAgencyCountByPer: '/ProBiddingAgency/GetProbiddingAgencyCountByPer',
  /** 判断招标代理是否存在已开票的 */
  HasYkpProBiddingAgency: '/ProBiddingAgency/HasYkpProBiddingAgency',
  /** 项目存档 */
  ProArchive: '/ProBiddingAgency/ProArchive',
  /** 项目存档新 */
  ProArchiveNew: '/ProBiddingAgency/ProArchiveNew',
  /** 项目存档接收 */
  ProArchiveSubmit: '/ProBiddingAgency/ProArchiveSubmit',
  /** 招标代理统计 */
  ProBiddingAgencyModelCount: '/ProBiddingAgency/ProBiddingAgencyModelCount',
  /** 招标代理项目提交审批 */
  ProBiddingAgencySubmit: '/ProBiddingAgency/ProBiddingAgencySubmit',
  /** 返回没有必填的招标代理文件配置名称 */
  RePbaFileName: '/ProBiddingAgency/RePbaFileName',
  /** 自检完成 */
  SelfCheck: '/ProBiddingAgency/SelfCheck',
  /** 设置招标代理项目是否活跃 */
  SetProBiddingAgencyIsActive: '/ProBiddingAgency/SetProBiddingAgencyIsActive',
  /** 提交互检 */
  SubmitCrossCheck: '/ProBiddingAgency/SubmitCrossCheck',
  /** 进入开标评标 */
  ToOpeningEvaluationBid: '/ProBiddingAgency/ToOpeningEvaluationBid',
  /** 进入项目存档 */
  ToProArchive: '/ProBiddingAgency/ToProArchive',
  /** 招标代理项目审批不通过 */
  UnExamineProBiddingAgency: '/ProBiddingAgency/UnExamineProBiddingAgency',
  /** 开标评标时间更新 */
  UpdateBidOpeningTime: '/ProBiddingAgency/UpdateBidOpeningTime',
  /** 中标通知时间更新 */
  UpdateBiddingNoticeTime: '/ProBiddingAgency/UpdateBiddingNoticeTime',
  /** 公告发布时间更新 */
  UpdateNoticePublishTime: '/ProBiddingAgency/UpdateNoticePublishTime',
  /** 修改招标代理项目 */
  UpdateProBiddingAgency: '/ProBiddingAgency/UpdateProBiddingAgency',
  /** 修改招标代理项目--新 */
  UpdateProBiddingAgencyNew: '/ProBiddingAgency/UpdateProBiddingAgencyNew',
  /** 修改招标代理项目--报告号 */
  UpdateProBiddingAgencyNo: '/ProBiddingAgency/UpdateProBiddingAgencyNo',
  /** 修改招标代理项目编号 */
  UpdateProBiddingAgencyPbaLetterNo: '/ProBiddingAgency/UpdateProBiddingAgencyPbaLetterNo',
  /** 修改招标代理项目招标文件预公示时间 */
  UpdateProBiddingAgencyPbaZbFileOpenTime: '/ProBiddingAgency/UpdateProBiddingAgencyPbaZbFileOpenTime',
  /** 修改招标代理项目 */
  UpdateProBiddingAgencySave: '/ProBiddingAgency/UpdateProBiddingAgencySave',
}
export const apiAutoProBiddingAgencyBonus = {
  /** 创建年度奖金 */
  AddProBiddingAgencyBonus: '/ProBiddingAgencyBonus/AddProBiddingAgencyBonus',
  /** 添加数据导入 */
  AddProBiddingAgencyBonusFile: '/ProBiddingAgencyBonus/AddProBiddingAgencyBonusFile',
  /** 添加发放 */
  AddProBiddingAgencyBonusGrant: '/ProBiddingAgencyBonus/AddProBiddingAgencyBonusGrant',
  /** 同步年度奖金(人员) */
  AsyncUserProBiddingAgencyBonus: '/ProBiddingAgencyBonus/AsyncUserProBiddingAgencyBonus',
  /** 同步年度奖金(效益工资) */
  AsyncXygzProBiddingAgencyBonus: '/ProBiddingAgencyBonus/AsyncXygzProBiddingAgencyBonus',
  /** 作废发放 */
  CancelProBiddingAgencyBonusGrant: '/ProBiddingAgencyBonus/CancelProBiddingAgencyBonusGrant',
  /** 删除年度奖金 */
  DeleteProBiddingAgencyBonus: '/ProBiddingAgencyBonus/DeleteProBiddingAgencyBonus',
  /** 导出年度奖金明细 */
  GetProBiddingAgencyBonusDetailExcel: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusDetailExcel',
  /** 获取年度奖金明细 */
  GetProBiddingAgencyBonusDetailList: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusDetailList',
  /** 获取年度奖金明细 */
  GetProBiddingAgencyBonusDetailSum: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusDetailSum',
  /** 添加数据导入 */
  GetProBiddingAgencyBonusFileList: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusFileList',
  /** 获取发放 */
  GetProBiddingAgencyBonusGrantList: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusGrantList',
  /** 获取年度奖金 */
  GetProBiddingAgencyBonusList: '/ProBiddingAgencyBonus/GetProBiddingAgencyBonusList',
  /** 更新年度奖金明细 */
  UpdateProBiddingAgencyBonusDetail: '/ProBiddingAgencyBonus/UpdateProBiddingAgencyBonusDetail',
}
export const apiAutoProBiddingAgencyCommission = {
  /** 添加招标代理项目提成 */
  AddProBiddingAgencyCommission: '/ProBiddingAgencyCommission/AddProBiddingAgencyCommission',
  /** 批量作废 */
  BatchCancelProBiddingAgencyCommission: '/ProBiddingAgencyCommission/BatchCancelProBiddingAgencyCommission',
  /** 批量更新招标代理项目提成 */
  BatchUpdateProBiddingAgencyCommission: '/ProBiddingAgencyCommission/BatchUpdateProBiddingAgencyCommission',
  /** 计算招标代理项目提成 */
  CalcProBiddingAgencyCommission: '/ProBiddingAgencyCommission/CalcProBiddingAgencyCommission',
  /** 删除招标代理项目提成 */
  DeleteProBiddingAgencyCommission: '/ProBiddingAgencyCommission/DeleteProBiddingAgencyCommission',
  /** 招标代理统计-复核人员导出 */
  ExportProBiddingAgencyCommissionExcelByProjectFhUser: '/ProBiddingAgencyCommission/ExportProBiddingAgencyCommissionExcelByProjectFhUser',
  /** 获取招标代理项目提成默认值 */
  GetProBiddingAgencyCommissionDefaultValue: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionDefaultValue',
  /** 招标代理统计-作业人员导出 */
  GetProBiddingAgencyCommissionExcelByProjectUser: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionExcelByProjectUser',
  /** 获取计算招标代理项目提成列表 */
  GetProBiddingAgencyCommissionList: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionList',
  /** 获取计算招标代理项目提成列表汇总 */
  GetProBiddingAgencyCommissionListSummary: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionListSummary',
  /** 获取招标代理项目提成下拉 */
  GetProBiddingAgencyCommissionSelect: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionSelect',
  /** 招标代理统计-全部（以部门为主体） */
  GetProBiddingAgencyCommissionStatisticsByDep: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByDep',
  /** 招标代理统计-全部（以部门为主体） */
  GetProBiddingAgencyCommissionStatisticsByDepSummary: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByDepSummary',
  /** 招标代理统计-部门（以本部门成员为主体） */
  GetProBiddingAgencyCommissionStatisticsByPer: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByPer',
  /** 招标代理统计-部门（以本部门成员为主体） */
  GetProBiddingAgencyCommissionStatisticsByPerSummary: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByPerSummary',
  /** 招标代理统计-全部（以项目为主体） */
  GetProBiddingAgencyCommissionStatisticsByPro: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByPro',
  /** 招标代理统计-全部（以项目为主体） */
  GetProBiddingAgencyCommissionStatisticsByProSummary: '/ProBiddingAgencyCommission/GetProBiddingAgencyCommissionStatisticsByProSummary',
  /** 同步招标代理项目提成 */
  SyncProBiddingAgencyCommission: '/ProBiddingAgencyCommission/SyncProBiddingAgencyCommission',
  /** 批量更新招标代理项目提成 */
  UpdateAll: '/ProBiddingAgencyCommission/UpdateAll',
  /** 更新招标代理项目提成 */
  UpdateProBiddingAgencyCommission: '/ProBiddingAgencyCommission/UpdateProBiddingAgencyCommission',
}
export const apiAutoProBiddingAgencyType = {
  /** 新增招标代理项目类型 */
  AddProBiddingAgencyType: '/ProBiddingAgencyType/AddProBiddingAgencyType',
  /** 删除招标代理项目类型 */
  DeleteProBiddingAgencyType: '/ProBiddingAgencyType/DeleteProBiddingAgencyType',
  /** 获取招标代理项目类型列表 */
  GetProBiddingAgencyTypeList: '/ProBiddingAgencyType/GetProBiddingAgencyTypeList',
  /** 修改招标代理项目类型 */
  UpdateProBiddingAgencyType: '/ProBiddingAgencyType/UpdateProBiddingAgencyType',
}
export const apiAutoProBiddingAgencyUpdate = {
  /** 新增招标代理项目变更 */
  AddProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/AddProBiddingAgencyUpdate',
  /** 删除招标代理项目变更 */
  DeleteProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/DeleteProBiddingAgencyUpdate',
  /** 审批通过招标代理项目变更 */
  ExamineProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/ExamineProBiddingAgencyUpdate',
  /** 获取招标代理项目变更列表 */
  GetProBiddingAgencyUpdateList: '/ProBiddingAgencyUpdate/GetProBiddingAgencyUpdateList',
  /** 获取招标代理项目变更列表统计 */
  GetProBiddingAgencyUpdateListCount: '/ProBiddingAgencyUpdate/GetProBiddingAgencyUpdateListCount',
  /** 获取招标代理项目变更--单条 */
  GetProBiddingAgencyUpdateSingle: '/ProBiddingAgencyUpdate/GetProBiddingAgencyUpdateSingle',
  /** 作废招标代理项目变更 */
  IsCancelProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/IsCancelProBiddingAgencyUpdate',
  /** 招标代理项目变更撤回 */
  ProBiddingAgencyUpdateReCall: '/ProBiddingAgencyUpdate/ProBiddingAgencyUpdateReCall',
  /** 招标代理项目变更提交审批 */
  ProBiddingAgencyUpdateSubmit: '/ProBiddingAgencyUpdate/ProBiddingAgencyUpdateSubmit',
  /** 招标代理项目变更撤回判断 */
  ProReCallJudge: '/ProBiddingAgencyUpdate/ProReCallJudge',
  /** 审批不通过招标代理项目变更 */
  UnExamineProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/UnExamineProBiddingAgencyUpdate',
  /** 修改招标代理项目变更 */
  UpdateProBiddingAgencyUpdate: '/ProBiddingAgencyUpdate/UpdateProBiddingAgencyUpdate',
}
export const apiAutoProBiddingOperation = {
  /** 新增投标项目 */
  AddProBiddingOperation: '/ProBiddingOperation/AddProBiddingOperation',
  /** 项目回档 */
  BackProArchive: '/ProBiddingOperation/BackProArchive',
  /** 作废投标项目 */
  CancelProBiddingOperation: '/ProBiddingOperation/CancelProBiddingOperation',
  /** 互检通过 */
  CrossCheck: '/ProBiddingOperation/CrossCheck',
  /** 互检打回 */
  CrossCheckBack: '/ProBiddingOperation/CrossCheckBack',
  /** 资料装订 */
  DataBindingProBiddingOperation: '/ProBiddingOperation/DataBindingProBiddingOperation',
  /** 删除投标项目 */
  DeleteProBiddingOperation: '/ProBiddingOperation/DeleteProBiddingOperation',
  /** 部门复核通过 */
  DepReview: '/ProBiddingOperation/DepReview',
  /** 部门复核打回 */
  DepReviewBack: '/ProBiddingOperation/DepReviewBack',
  /** 部门复核通过 */
  DepReviewEnd: '/ProBiddingOperation/DepReviewEnd',
  /** 进入存档 */
  EnterArchive: '/ProBiddingOperation/EnterArchive',
  /** 投标项目审批通过 */
  ExamineProBiddingOperation: '/ProBiddingOperation/ExamineProBiddingOperation',
  /** 项目签发审批通过 */
  ExamineProIssue: '/ProBiddingOperation/ExamineProIssue',
  /** 投标作业左侧菜单统计 */
  GetPboMenuCount: '/ProBiddingOperation/GetPboMenuCount',
  /** 获取投标项目列表 */
  GetProBiddingOperationList: '/ProBiddingOperation/GetProBiddingOperationList',
  /** 获取投标项目列表-状态计数 */
  GetProBiddingOperationListCount: '/ProBiddingOperation/GetProBiddingOperationListCount',
  /** 获取投标项目列表-统计数量 */
  GetProBiddingOperationListSum: '/ProBiddingOperation/GetProBiddingOperationListSum',
  /** 获取投标项目互检列表 */
  GetProBiddingOperationReCheckList: '/ProBiddingOperation/GetProBiddingOperationReCheckList',
  /** 获取投标项目复核列表 */
  GetProBiddingOperationReviewList: '/ProBiddingOperation/GetProBiddingOperationReviewList',
  /** 获取投标项目--单条 */
  GetProBiddingOperationSingle: '/ProBiddingOperation/GetProBiddingOperationSingle',
  /** 项目存档 */
  ProArchive: '/ProBiddingOperation/ProArchive',
  /** 提交项目归档 */
  ProArchiveSubmit: '/ProBiddingOperation/ProArchiveSubmit',
  /** 投标项目提交审批 */
  ProBiddingOperationSubmit: '/ProBiddingOperation/ProBiddingOperationSubmit',
  /** 项目签发提交审批 */
  ProIssueSubmit: '/ProBiddingOperation/ProIssueSubmit',
  /** 返回没有必填的招标代理文件配置名称 */
  RePboFileName: '/ProBiddingOperation/RePboFileName',
  /** 项目签发撤回 */
  RecallProIssue: '/ProBiddingOperation/RecallProIssue',
  /** 自检完成 */
  SelfCheck: '/ProBiddingOperation/SelfCheck',
  /** 提交互检 */
  SubmitCrossCheck: '/ProBiddingOperation/SubmitCrossCheck',
  /** 投标文件 */
  SubmitCrossPboFile: '/ProBiddingOperation/SubmitCrossPboFile',
  /** 进入开标评标 */
  ToOpeningEvaluationBid: '/ProBiddingOperation/ToOpeningEvaluationBid',
  /** 进入项目存档 */
  ToProArchive: '/ProBiddingOperation/ToProArchive',
  /** 投标项目审批不通过 */
  UnExamineProBiddingOperation: '/ProBiddingOperation/UnExamineProBiddingOperation',
  /** 项目签发审批不通过 */
  UnExamineProIssue: '/ProBiddingOperation/UnExamineProIssue',
  /** 开标评标时间更新 */
  UpdateBidOpeningTime: '/ProBiddingOperation/UpdateBidOpeningTime',
  /** 中标通知时间更新 */
  UpdateBiddingNoticeTime: '/ProBiddingOperation/UpdateBiddingNoticeTime',
  /** 公告发布时间更新 */
  UpdateNoticePublishTime: '/ProBiddingOperation/UpdateNoticePublishTime',
  /** 修改投标项目 */
  UpdateProBiddingOperation: '/ProBiddingOperation/UpdateProBiddingOperation',
  /** 修改投标项目--新 */
  UpdateProBiddingOperationNew: '/ProBiddingOperation/UpdateProBiddingOperationNew',
}
export const apiAutoProBiddingOperationUpdate = {
  /** 新增投标项目变更 */
  AddProBiddingOperationUpdate: '/ProBiddingOperationUpdate/AddProBiddingOperationUpdate',
  /** 删除投标项目变更 */
  DeleteProBiddingOperationUpdate: '/ProBiddingOperationUpdate/DeleteProBiddingOperationUpdate',
  /** 审批通过投标项目变更 */
  ExamineProBiddingOperationUpdate: '/ProBiddingOperationUpdate/ExamineProBiddingOperationUpdate',
  /** 获取投标项目变更列表 */
  GetProBiddingOperationUpdateList: '/ProBiddingOperationUpdate/GetProBiddingOperationUpdateList',
  /** 获取投标项目变更列表统计 */
  GetProBiddingOperationUpdateListCount: '/ProBiddingOperationUpdate/GetProBiddingOperationUpdateListCount',
  /** 获取投标项目变更--单条 */
  GetProBiddingOperationUpdateSingle: '/ProBiddingOperationUpdate/GetProBiddingOperationUpdateSingle',
  /** 作废投标项目变更 */
  IsCancelProBiddingOperationUpdate: '/ProBiddingOperationUpdate/IsCancelProBiddingOperationUpdate',
  /** 投标项目变更撤回 */
  ProBiddingOperationUpdateReCall: '/ProBiddingOperationUpdate/ProBiddingOperationUpdateReCall',
  /** 投标项目变更提交审批 */
  ProBiddingOperationUpdateSubmit: '/ProBiddingOperationUpdate/ProBiddingOperationUpdateSubmit',
  /** 投标项目变更撤回判断 */
  ProReCallJudge: '/ProBiddingOperationUpdate/ProReCallJudge',
  /** 审批不通过投标项目变更 */
  UnExamineProBiddingOperationUpdate: '/ProBiddingOperationUpdate/UnExamineProBiddingOperationUpdate',
  /** 修改投标项目变更 */
  UpdateProBiddingOperationUpdate: '/ProBiddingOperationUpdate/UpdateProBiddingOperationUpdate',
}
export const apiAutoProCaseType = {
  /** 新增项目案例类别 */
  AddProCaseType: '/ProCaseType/AddProCaseType',
  /** 删除项目案例类别 */
  DeleteProCaseType: '/ProCaseType/DeleteProCaseType',
  /** 获取项目案例类别列表 */
  GetProCaseTypeList: '/ProCaseType/GetProCaseTypeList',
  /** 是否设置为优秀案例 */
  IsSetExcellentProCaseType: '/ProCaseType/IsSetExcellentProCaseType',
  /** 修改项目案例类别 */
  UpdateProCaseType: '/ProCaseType/UpdateProCaseType',
}
export const apiAutoProFhQuestionCount = {
  /** 复核问题统计--按部门导出 */
  ExportProFhQuestionCountList: '/ProFhQuestionCount/ExportProFhQuestionCountList',
  /** 导出复核问题统计 */
  ExportProFhQuestionCountListByPer: '/ProFhQuestionCount/ExportProFhQuestionCountListByPer',
  /** 复核问题统计--按部门 */
  GetProFhQuestionCountList: '/ProFhQuestionCount/GetProFhQuestionCountList',
  /** 复核问题统计--按人 */
  GetProFhQuestionCountListByPer: '/ProFhQuestionCount/GetProFhQuestionCountListByPer',
}
export const apiAutoProHousingConstruction = {
  /** 新增房屋建筑工程 */
  AddProHousingConstruction: '/ProHousingConstruction/AddProHousingConstruction',
  /** 删除房屋建筑工程 */
  DeleteProHousingConstruction: '/ProHousingConstruction/DeleteProHousingConstruction',
  /** 获取房屋建筑工程列表 */
  GetProHousingConstructionList: '/ProHousingConstruction/GetProHousingConstructionList',
  /** 获取房屋建筑工程单条 */
  GetProHousingConstructionSingle: '/ProHousingConstruction/GetProHousingConstructionSingle',
  /** 修改房屋建筑工程 */
  UpdateProHousingConstruction: '/ProHousingConstruction/UpdateProHousingConstruction',
  /** 导入房屋建筑工程 */
  UploadProHousingConstruction: '/ProHousingConstruction/UploadProHousingConstruction',
}
export const apiAutoProImgDisk = {
  /** 批量新增相册 */
  AddBathProImgDisk: '/ProImgDisk/AddBathProImgDisk',
  /** 新增项目相册 */
  AddProImgDisk: '/ProImgDisk/AddProImgDisk',
  /** 复制到 */
  CopyProImgDisk: '/ProImgDisk/CopyProImgDisk',
  /** 删除相册--真实删除 */
  DeleteProImgDisk: '/ProImgDisk/DeleteProImgDisk',
  /** 清空回收箱---对应项目下的 */
  EmptyCollectionBox: '/ProImgDisk/EmptyCollectionBox',
  /** 获取项目相册权限人员列表 */
  GetImgPowerList: '/ProImgDisk/GetImgPowerList',
  /** 获取项目相册列表 */
  GetProImgDiskList: '/ProImgDisk/GetProImgDiskList',
  /** 获取相册路径 */
  GetProImgDiskRoute: '/ProImgDisk/GetProImgDiskRoute',
  /** 删除相册--逻辑删除 */
  LogicDeleteProImgDisk: '/ProImgDisk/LogicDeleteProImgDisk',
  /** 删除相册--逻辑删除  恢复 */
  LogicDeleteRecoverProImgDisk: '/ProImgDisk/LogicDeleteRecoverProImgDisk',
  /** 移动到 */
  MoveProImgDisk: '/ProImgDisk/MoveProImgDisk',
  /** 赋权限 */
  PowerProImgDisk: '/ProImgDisk/PowerProImgDisk',
  /** 修改项目相册 */
  UpdateProImgDisk: '/ProImgDisk/UpdateProImgDisk',
}
export const apiAutoProMajor = {
  /** 新增专业配置 */
  AddProMajor: '/ProMajor/AddProMajor',
  /** 删除专业配置 */
  DeleteProMajor: '/ProMajor/DeleteProMajor',
  /** 获取专业配置列表 */
  GetProMajorList: '/ProMajor/GetProMajorList',
  /** 修改专业配置 */
  UpdateProMajor: '/ProMajor/UpdateProMajor',
}
export const apiAutoProManage = {
  /** 新增项目管理项目 */
  AddProManage: '/ProManage/AddProManage',
  /** 删除项目管理项目 */
  DeleteProManage: '/ProManage/DeleteProManage',
  /** 全过程咨询项目审批通过 */
  ExamineProManage: '/ProManage/ExamineProManage',
  /** 日报和巡检统计 */
  GetProManageDailyManageAndInspectionCount: '/ProManage/GetProManageDailyManageAndInspectionCount',
  /** 获取项目管理项目列表 */
  GetProManageList: '/ProManage/GetProManageList',
  /** 项目管理项目统计 */
  GetProManageListCount: '/ProManage/GetProManageListCount',
  /** 获取项目管理项目单条 */
  GetProManageSingle: '/ProManage/GetProManageSingle',
  /** 全过程员工业绩库 */
  GetProjectManageUserWithAchievementList: '/ProManage/GetProjectManageUserWithAchievementList',
  /** 获取单条项目业绩库--项目,整体项目,合同,相关单位信息 */
  GetSingleProManageAchievement: '/ProManage/GetSingleProManageAchievement',
  /** 全过程咨询项目提交审批 */
  ProManageSubmit: '/ProManage/ProManageSubmit',
  /** 全过程咨询 */
  QcgzxCount: '/ProManage/QcgzxCount',
  /** 设置项管是否活跃 */
  SetProManageActive: '/ProManage/SetProManageActive',
  /** 全过程咨询项目审批不通过 */
  UnExamineProManage: '/ProManage/UnExamineProManage',
  /** 修改项目管理项目 */
  UpdateProManage: '/ProManage/UpdateProManage',
  /** 修改项目业绩库 */
  UpdateProManageAchievement: '/ProManage/UpdateProManageAchievement',
}
export const apiAutoProManageDep = {
  /** 新增项目管理项目部门 */
  AddProManageDep: '/ProManageDep/AddProManageDep',
  /** 删除项目管理项目部门 */
  DeleteProManageDep: '/ProManageDep/DeleteProManageDep',
  /** 获取项目管理项目部门列表 */
  GetProManageDepList: '/ProManageDep/GetProManageDepList',
  /** 获取项目管理项目部门单条 */
  GetProManageDepSingle: '/ProManageDep/GetProManageDepSingle',
  /** 修改项目管理项目部门 */
  UpdateProManageDep: '/ProManageDep/UpdateProManageDep',
}
export const apiAutoProManageOrganization = {
  /** 新增项目组织信息 */
  AddProManageOrganization: '/ProManageOrganization/AddProManageOrganization',
  /** 删除项目组织信息 */
  DeleteProManageOrganization: '/ProManageOrganization/DeleteProManageOrganization',
  /** 获取项目组织信息 */
  GetProManageOrganizationList: '/ProManageOrganization/GetProManageOrganizationList',
  /** 修改项目组织信息 */
  UpdateProManageOrganization: '/ProManageOrganization/UpdateProManageOrganization',
}
export const apiAutoProManageUnit = {
  /** 新增项目组织单位信息 */
  AddProManageUnit: '/ProManageUnit/AddProManageUnit',
  /** 删除项目组织单位信息 */
  DeleteProManageUnit: '/ProManageUnit/DeleteProManageUnit',
  /** 获取项目组织单位信息 */
  GetProManageUnitList: '/ProManageUnit/GetProManageUnitList',
  /** 修改项目组织单位信息 */
  UpdateProManageUnit: '/ProManageUnit/UpdateProManageUnit',
}
export const apiAutoProManageUnitPer = {
  /** 新增项目组织单位联系人信息 */
  AddProManageUnitPer: '/ProManageUnitPer/AddProManageUnitPer',
  /** 删除项目组织单位联系人信息 */
  DeleteProManageUnitPer: '/ProManageUnitPer/DeleteProManageUnitPer',
  /** 获取项目组织单位联系人信息 */
  GetProManageUnitPerList: '/ProManageUnitPer/GetProManageUnitPerList',
  /** 修改项目组织单位联系人信息 */
  UpdateProManageUnitPer: '/ProManageUnitPer/UpdateProManageUnitPer',
}
export const apiAutoProManageUser = {
  /** 新增项目管理项目人员 */
  AddProManageUser: '/ProManageUser/AddProManageUser',
  /** 新增项目管理项目人员--批量 */
  AddProManageUserBath: '/ProManageUser/AddProManageUserBath',
  /** 删除项目管理项目人员 */
  DeleteProManageUser: '/ProManageUser/DeleteProManageUser',
  /** 获取项目管理项目人员列表 */
  GetProManageUserList: '/ProManageUser/GetProManageUserList',
  /** 获取项目管理项目人员单条 */
  GetProManageUserSingle: '/ProManageUser/GetProManageUserSingle',
  /** 修改项目管理项目人员 */
  UpdateProManageUser: '/ProManageUser/UpdateProManageUser',
}
export const apiAutoProNetworkDisk = {
  /** 批量新增网盘 */
  AddBathProNetworkDisk: '/ProNetworkDisk/AddBathProNetworkDisk',
  /** 新增项目网盘 */
  AddProNetworkDisk: '/ProNetworkDisk/AddProNetworkDisk',
  /** 复制到 */
  CopyProNetworkDisk: '/ProNetworkDisk/CopyProNetworkDisk',
  /** 删除网盘--真实删除 */
  DeleteProNetworkDisk: '/ProNetworkDisk/DeleteProNetworkDisk',
  /** 清空回收箱---对应项目下的 */
  EmptyCollectionBox: '/ProNetworkDisk/EmptyCollectionBox',
  /** 获取项目网盘权限人员列表 */
  GetNetworkPowerList: '/ProNetworkDisk/GetNetworkPowerList',
  /** 获取项目网盘列表 */
  GetProNetworkDiskList: '/ProNetworkDisk/GetProNetworkDiskList',
  /** 获取网盘路径 */
  GetProNetworkDiskRoute: '/ProNetworkDisk/GetProNetworkDiskRoute',
  /** 删除网盘--逻辑删除 */
  LogicDeleteProNetworkDisk: '/ProNetworkDisk/LogicDeleteProNetworkDisk',
  /** 删除网盘--逻辑删除  恢复 */
  LogicDeleteRecoverProNetworkDisk: '/ProNetworkDisk/LogicDeleteRecoverProNetworkDisk',
  /** 移动到 */
  MoveProNetworkDisk: '/ProNetworkDisk/MoveProNetworkDisk',
  /** 赋权限 */
  PowerProNetworkDisk: '/ProNetworkDisk/PowerProNetworkDisk',
  /** 修改项目网盘 */
  UpdateProNetworkDisk: '/ProNetworkDisk/UpdateProNetworkDisk',
}
export const apiAutoProOperationLog = {
  /** 获取项目操作日志 */
  GetProOperationLogList: '/ProOperationLog/GetProOperationLogList',
}
export const apiAutoProOverprint = {
  /** 删除项目增印 */
  DeleteProjectOverprint: '/ProOverprint/DeleteProjectOverprint',
  /** 获取项目增印列表 */
  GetProjectOverprintList: '/ProOverprint/GetProjectOverprintList',
  /** 判断能否提交项目增印 */
  JudgeProjectOverprint: '/ProOverprint/JudgeProjectOverprint',
  /** 项目增印撤回 */
  ProjectOverprintReCall: '/ProOverprint/ProjectOverprintReCall',
  /** 新增项目增印 */
  SubmitProjectOverprint: '/ProOverprint/SubmitProjectOverprint',
}
export const apiAutoProReportConfig = {
  /** 新增项目报表配置 */
  AddProReportConfig: '/ProReportConfig/AddProReportConfig',
  /** 删除项目报表配置 */
  DeleteProReportConfig: '/ProReportConfig/DeleteProReportConfig',
  /** 获取项目报表配置列表 */
  GetProReportConfigList: '/ProReportConfig/GetProReportConfigList',
  /** 获取项目报表配置单条 */
  GetProReportConfigSingle: '/ProReportConfig/GetProReportConfigSingle',
  /** 修改项目报表配置 */
  UpdateProReportConfig: '/ProReportConfig/UpdateProReportConfig',
}
export const apiAutoProReviewRemark = {
  /** 新增项目复核备注 */
  AddProReviewRemark: '/ProReviewRemark/AddProReviewRemark',
  /** 删除项目复核备注 */
  DeleteProReviewRemark: '/ProReviewRemark/DeleteProReviewRemark',
  /** 获取项目复核备注列表 */
  GetProReviewRemarkList: '/ProReviewRemark/GetProReviewRemarkList',
  /** 修改项目复核备注 */
  UpdateProReviewRemark: '/ProReviewRemark/UpdateProReviewRemark',
}
export const apiAutoProSettleindexGroup = {
  /** 新增项目结算指标组 */
  AddProSettleindexGroup: '/ProSettleindexGroup/AddProSettleindexGroup',
  /** 删除项目结算指标组 */
  DeleteProSettleindexGroup: '/ProSettleindexGroup/DeleteProSettleindexGroup',
  /** 获取项目结算指标组列表 */
  GetProSettleindexGroupList: '/ProSettleindexGroup/GetProSettleindexGroupList',
  /** 修改项目结算指标组 */
  UpdateProSettleindexGroup: '/ProSettleindexGroup/UpdateProSettleindexGroup',
}
export const apiAutoProStandardType = {
  /** 新增类型配置 */
  AddProStandardType: '/ProStandardType/AddProStandardType',
  /** 删除类型配置 */
  DeleteProStandardType: '/ProStandardType/DeleteProStandardType',
  /** 获取类型配置列表 */
  GetProStandardTypeList: '/ProStandardType/GetProStandardTypeList',
  /** 修改类型配置 */
  UpdateProStandardType: '/ProStandardType/UpdateProStandardType',
}
export const apiAutoProSubitemConfig = {
  /** 新增分部分项配置 */
  AddProSubitemConfig: '/ProSubitemConfig/AddProSubitemConfig',
  /** 删除分部分项配置 */
  DeleteProSubitemConfig: '/ProSubitemConfig/DeleteProSubitemConfig',
  /** 获取分部分项配置列表 */
  GetProSubitemConfigList: '/ProSubitemConfig/GetProSubitemConfigList',
  /** 修改分部分项配置 */
  UpdateProSubitemConfig: '/ProSubitemConfig/UpdateProSubitemConfig',
}
export const apiAutoProSurveyRecord = {
  /** 新增踏勘记录信息 */
  AddProSurveyRecord: '/ProSurveyRecord/AddProSurveyRecord',
  /** 作废踏勘记录信息 */
  CancelProSurveyRecord: '/ProSurveyRecord/CancelProSurveyRecord',
  /** 删除踏勘记录信息 */
  DeleteProSurveyRecord: '/ProSurveyRecord/DeleteProSurveyRecord',
  /** 获取踏勘记录信息 */
  GetProSurveyRecordList: '/ProSurveyRecord/GetProSurveyRecordList',
  /** 获取踏勘记录信息 */
  GetProSurveyRecordSingle: '/ProSurveyRecord/GetProSurveyRecordSingle',
  /** 修改踏勘记录信息 */
  UpdateProSurveyRecord: '/ProSurveyRecord/UpdateProSurveyRecord',
}
export const apiAutoProTgConfig = {
  /** 新增技术措施 */
  AddProTgConfig: '/ProTgConfig/AddProTgConfig',
  /** 删除技术措施 */
  DeleteProTgConfig: '/ProTgConfig/DeleteProTgConfig',
  /** 获取技术措施列表 */
  GetProTgConfigList: '/ProTgConfig/GetProTgConfigList',
  /** 修改技术措施 */
  UpdateProTgConfig: '/ProTgConfig/UpdateProTgConfig',
}
export const apiAutoProTyConfig = {
  /** 新增通用配置 */
  AddProTyConfig: '/ProTyConfig/AddProTyConfig',
  /** 删除通用配置 */
  DeleteProTyConfig: '/ProTyConfig/DeleteProTyConfig',
  /** 获取通用配置列表 */
  GetProTyConfigList: '/ProTyConfig/GetProTyConfigList',
  /** 修改通用配置 */
  UpdateProTyConfig: '/ProTyConfig/UpdateProTyConfig',
}
export const apiAutoProjectBidding = {
  /** 新增招标控制价审批统一口径 */
  AddProjectBidding: '/ProjectBidding/AddProjectBidding',
  /** 删除招标控制价审批统一口径 */
  DeleteProjectBidding: '/ProjectBidding/DeleteProjectBidding',
  /** 获取招标控制价审批统一口径 */
  GetProjectBiddingList: '/ProjectBidding/GetProjectBiddingList',
  /** 获取招标控制价审批统一口径--单条 */
  GetProjectBiddingSingle: '/ProjectBidding/GetProjectBiddingSingle',
  /** 修改招标控制价审批统一口径 */
  UpdateProjectBidding: '/ProjectBidding/UpdateProjectBidding',
}
export const apiAutoProjectBonus = {
  /** 创建年度奖金 */
  AddProjectBonus: '/ProjectBonus/AddProjectBonus',
  /** 添加数据导入 */
  AddProjectBonusFile: '/ProjectBonus/AddProjectBonusFile',
  /** 添加发放 */
  AddProjectBonusGrant: '/ProjectBonus/AddProjectBonusGrant',
  /** 同步年度奖金(人员) */
  AsyncUserProjectBonus: '/ProjectBonus/AsyncUserProjectBonus',
  /** 同步年度奖金(效益工资) */
  AsyncXygzProjectBonus: '/ProjectBonus/AsyncXygzProjectBonus',
  /** 作废发放 */
  CancelProjectBonusGrant: '/ProjectBonus/CancelProjectBonusGrant',
  /** 删除年度奖金 */
  DeleteProjectBonus: '/ProjectBonus/DeleteProjectBonus',
  /** 导出年度奖金明细 */
  GetProjectBonusDetailExcel: '/ProjectBonus/GetProjectBonusDetailExcel',
  /** 获取年度奖金明细 */
  GetProjectBonusDetailList: '/ProjectBonus/GetProjectBonusDetailList',
  /** 获取年度奖金明细 */
  GetProjectBonusDetailSum: '/ProjectBonus/GetProjectBonusDetailSum',
  /** 添加数据导入 */
  GetProjectBonusFileList: '/ProjectBonus/GetProjectBonusFileList',
  /** 获取发放 */
  GetProjectBonusGrantList: '/ProjectBonus/GetProjectBonusGrantList',
  /** 获取年度奖金 */
  GetProjectBonusList: '/ProjectBonus/GetProjectBonusList',
  /** 更新年度奖金明细 */
  UpdateProjectBonusDetail: '/ProjectBonus/UpdateProjectBonusDetail',
}
export const apiAutoProjectBulletinboard = {
  /** 新增项目看板 */
  AddProjectBulletinboard: '/ProjectBulletinboard/AddProjectBulletinboard',
  /** 删除项目看板 */
  DeleteProjectBulletinboard: '/ProjectBulletinboard/DeleteProjectBulletinboard',
  /** 获取项目看板列表 */
  GetProjectBulletinboardList: '/ProjectBulletinboard/GetProjectBulletinboardList',
  /** 修改项目看板 */
  UpdateProjectBulletinboard: '/ProjectBulletinboard/UpdateProjectBulletinboard',
}
export const apiAutoProjectCheckQuestion = {
  /** 新增项目复核细节问题 */
  AddProjectCheckQuestion: '/ProjectCheckQuestion/AddProjectCheckQuestion',
  /** 删除项目复核细节问题 */
  DeleteProjectCheckQuestion: '/ProjectCheckQuestion/DeleteProjectCheckQuestion',
  /** 获取项目复核细节问题列表 */
  GetProjectCheckQuestionList: '/ProjectCheckQuestion/GetProjectCheckQuestionList',
  /** 修改项目复核细节问题 */
  UpdateProjectCheckQuestion: '/ProjectCheckQuestion/UpdateProjectCheckQuestion',
}
export const apiAutoProjectCustomer = {
  /** 新增项目单位 */
  AddProjectCustomer: '/ProjectCustomer/AddProjectCustomer',
  /** 删除项目单位 */
  DeleteProjectCustomer: '/ProjectCustomer/DeleteProjectCustomer',
  /** 获取全过程咨询所有标段合同下的施工单位 */
  GetPmAgreementProjectCustomerList: '/ProjectCustomer/GetPmAgreementProjectCustomerList',
  /** 获取项目单位列表 */
  GetProjectCustomerList: '/ProjectCustomer/GetProjectCustomerList',
  /** 修改项目单位 */
  UpdateProjectCustomer: '/ProjectCustomer/UpdateProjectCustomer',
}
export const apiAutoProjectDep = {
  /** 新增项目部门 */
  AddProjectDep: '/ProjectDep/AddProjectDep',
  /** 删除项目部门 */
  DeleteProjectDep: '/ProjectDep/DeleteProjectDep',
  /** 获取项目部门列表 */
  GetProjectDepList: '/ProjectDep/GetProjectDepList',
  /** 修改项目部门 */
  UpdateProjectDep: '/ProjectDep/UpdateProjectDep',
}
export const apiAutoProjectEstimateBudget = {
  /** 新增概算审批统一口径 */
  AddProjectEstimateBudget: '/ProjectEstimateBudget/AddProjectEstimateBudget',
  /** 删除概算审批统一口径 */
  DeleteProjectEstimateBudget: '/ProjectEstimateBudget/DeleteProjectEstimateBudget',
  /** 获取概算审批统一口径 */
  GetProjectEstimateBudgetList: '/ProjectEstimateBudget/GetProjectEstimateBudgetList',
  /** 获取概算审批统一口径--单条 */
  GetProjectEstimateBudgetSingle: '/ProjectEstimateBudget/GetProjectEstimateBudgetSingle',
  /** 修改概算审批统一口径 */
  UpdateProjectEstimateBudget: '/ProjectEstimateBudget/UpdateProjectEstimateBudget',
}
export const apiAutoProjectFile = {
  /** 新增项目文件 */
  AddProjectFile: '/ProjectFile/AddProjectFile',
  /** 批量新增项目文件 */
  AddProjectFileBath: '/ProjectFile/AddProjectFileBath',
  /** 删除项目文件 */
  DeleteProjectFile: '/ProjectFile/DeleteProjectFile',
  /** 获取项目文件列表 */
  GetProjectFileList: '/ProjectFile/GetProjectFileList',
  /** 获取项目文件类型列表 */
  GetProjectFiletypeList: '/ProjectFile/GetProjectFiletypeList',
  /** 修改项目文件 */
  UpdateProjectFile: '/ProjectFile/UpdateProjectFile',
}
export const apiAutoProjectFileGd = {
  /** 新增项目归档文件 */
  AddProjectFileGd: '/ProjectFileGd/AddProjectFileGd',
  /** 批量新增项目归档文件 */
  AddProjectFileGdBath: '/ProjectFileGd/AddProjectFileGdBath',
  /** 删除项目归档文件 */
  DeleteProjectFileGd: '/ProjectFileGd/DeleteProjectFileGd',
  /** 获取项目归档文件列表 */
  GetProjectFileGdList: '/ProjectFileGd/GetProjectFileGdList',
  /** 修改项目归档文件 */
  UpdateProjectFileGd: '/ProjectFileGd/UpdateProjectFileGd',
}
export const apiAutoProjectFileJs = {
  /** 新增项目接收文件 */
  AddProjectFileJs: '/ProjectFileJs/AddProjectFileJs',
  /** 批量新增项目接收文件 */
  AddProjectFileJsBath: '/ProjectFileJs/AddProjectFileJsBath',
  /** 删除项目接收文件 */
  DeleteProjectFileJs: '/ProjectFileJs/DeleteProjectFileJs',
  /** 获取项目接收文件列表 */
  GetProjectFileJsList: '/ProjectFileJs/GetProjectFileJsList',
  /** 修改项目接收文件 */
  UpdateProjectFileJs: '/ProjectFileJs/UpdateProjectFileJs',
}
export const apiAutoProjectInfo = {
  /** 新增项目信息 */
  AddProjectInfo: '/ProjectInfo/AddProjectInfo',
  /** 取消第三方复核 */
  CancelDsffh: '/ProjectInfo/CancelDsffh',
  /** 处理项目实际用章人 结构 */
  ClProjectSjyzPerjg: '/ProjectInfo/ClProjectSjyzPerjg',
  /** 获取我的代办统计 */
  CommonUpdatePiRelationTable: '/ProjectInfo/CommonUpdatePiRelationTable',
  /** 复制项目 */
  CopyProjectInfo: '/ProjectInfo/CopyProjectInfo',
  /** 统计指定项目的所有数据综合 */
  CountProjectPrice: '/ProjectInfo/CountProjectPrice',
  /** 生成无报告文号 */
  CreateNoReportNo: '/ProjectInfo/CreateNoReportNo',
  /** 生成报告文号 */
  CreateReportNo: '/ProjectInfo/CreateReportNo',
  /** 删除项目信息 */
  DeleteProjectInfo: '/ProjectInfo/DeleteProjectInfo',
  /** 存档表格导出 */
  DownloadAndZipFiles: '/ProjectInfo/DownloadAndZipFiles',
  /** 第三方复核打回 */
  DsffhBack: '/ProjectInfo/DsffhBack',
  /** 第三方复核完成 */
  DsffhComplete: '/ProjectInfo/DsffhComplete',
  /** 项目签发审批通过 */
  ExamineProIssue: '/ProjectInfo/ExamineProIssue',
  /** 项目审批通过 */
  ExamineProjectInfo: '/ProjectInfo/ExamineProjectInfo',
  /** 财务审计项目业绩库 */
  ExportCwsjProjectInfoPerformance: '/ProjectInfo/ExportCwsjProjectInfoPerformance',
  /** 导出杭州造价咨询 */
  ExportEntriesInfo: '/ProjectInfo/ExportEntriesInfo',
  /** 导出归档报告 */
  ExportGdProjectInfo: '/ProjectInfo/ExportGdProjectInfo',
  /** 导出丽水造价咨询项目 */
  ExportLiShuiInfoNew: '/ProjectInfo/ExportLiShuiInfoNew',
  /** 丽水上报导出 */
  ExportLisShuiInfoNew: '/ProjectInfo/ExportLisShuiInfoNew',
  /** 导出杭州造价咨询 */
  ExportNingBoInfo: '/ProjectInfo/ExportNingBoInfo',
  /** 导出宁波造价咨询项目 */
  ExportNingBoInfoNew: '/ProjectInfo/ExportNingBoInfoNew',
  /** 导出项目案例 */
  ExportProCaseFile: '/ProjectInfo/ExportProCaseFile',
  /** 存档表格导出 */
  ExportProjectCdbg: '/ProjectInfo/ExportProjectCdbg',
  /** 导出造价咨询项目 */
  ExportProjectInfo: '/ProjectInfo/ExportProjectInfo',
  /** 前期咨询项目业绩库 */
  ExportQqzxProjectInfoPerformance: '/ProjectInfo/ExportQqzxProjectInfoPerformance',
  /** 财务审计项目信息(审定数) */
  ExportSdyFinancialReview: '/ProjectInfo/ExportSdyFinancialReview',
  /** 税务审计项目业绩库 */
  ExportSwsjProjectInfoPerformance: '/ProjectInfo/ExportSwsjProjectInfoPerformance',
  /** 资产评估项目业绩库 */
  ExportZcpgProjectInfoPerformance: '/ProjectInfo/ExportZcpgProjectInfoPerformance',
  /** 造价咨询项目信息导出 */
  ExportZjProjectInfo: '/ProjectInfo/ExportZjProjectInfo',
  /** 造价咨询项目业绩库 */
  ExportZjzxProjectInfoPerformance: '/ProjectInfo/ExportZjzxProjectInfoPerformance',
  /** 初步文字复核打回 */
  FirstStepWordsBack: '/ProjectInfo/FirstStepWordsBack',
  /** 获取我的代办统计 */
  GetCommissionCount: '/ProjectInfo/GetCommissionCount',
  /** 获取项目提成信息用于创收 */
  GetCommissionInfoForCreateIncome: '/ProjectInfo/GetCommissionInfoForCreateIncome',
  /** 获取我的代办项目 */
  GetCommissionProjectList: '/ProjectInfo/GetCommissionProjectList',
  /** 沟通记录统计 */
  GetCommunicateCount: '/ProjectInfo/GetCommunicateCount',
  /** 公司复核结果统计 */
  GetCompanyFhResultCountList: '/ProjectInfo/GetCompanyFhResultCountList',
  /** 公司复核结果统计 */
  GetCompanyFhResultCountListNew: '/ProjectInfo/GetCompanyFhResultCountListNew',
  /** 部门复核结果统计 */
  GetDepFhResultCountList: '/ProjectInfo/GetDepFhResultCountList',
  /** 导出复核结果统计-部门 */
  GetDepFhResultCountListExcel: '/ProjectInfo/GetDepFhResultCountListExcel',
  /** 人员项目统计--跟踪审计 */
  GetGzsjPerProCountList: '/ProjectInfo/GetGzsjPerProCountList',
  /** 人员项目统计--跟踪审计相关项目 */
  GetGzsjPerProDeatilList: '/ProjectInfo/GetGzsjPerProDeatilList',
  /** 人员项目统计--跟踪审计相关项目 */
  GetGzsjPerProDeatilListCount: '/ProjectInfo/GetGzsjPerProDeatilListCount',
  /** 首页统计 */
  GetIndexCount: '/ProjectInfo/GetIndexCount',
  /** 首页统计--测试 */
  GetIndexCountCopy: '/ProjectInfo/GetIndexCountCopy',
  /** 我创建的项目 */
  GetOwnProjectList: '/ProjectInfo/GetOwnProjectList',
  /** 人员项目统计--招标代理 */
  GetPbaPerProCountList: '/ProjectInfo/GetPbaPerProCountList',
  /** 人员项目统计--招标代理 */
  GetPbaPerProDeatilList: '/ProjectInfo/GetPbaPerProDeatilList',
  /** 人员项目统计--招标代理 */
  GetPbaPerProDeatilListCount: '/ProjectInfo/GetPbaPerProDeatilListCount',
  /** 获取造价咨询-评估-关系表-被评估方子表列表 */
  GetPiEstimateEstimatedInfoList: '/ProjectInfo/GetPiEstimateEstimatedInfoList',
  /** 获取造价咨询-评估-关系表-相关人员子表列表 */
  GetPiEstimatePerList: '/ProjectInfo/GetPiEstimatePerList',
  /** 获取造价咨询-财审-关系表子表列表 */
  GetPiFinancialReviewSonList: '/ProjectInfo/GetPiFinancialReviewSonList',
  /** 人员项目统计-全过程咨询 */
  GetPmPerProCountList: '/ProjectInfo/GetPmPerProCountList',
  /** 人员项目统计-全过程咨询相关项目 */
  GetPmPerProDeatilList: '/ProjectInfo/GetPmPerProDeatilList',
  /** 人员项目统计-全过程咨询相关项目 */
  GetPmPerProDeatilListCount: '/ProjectInfo/GetPmPerProDeatilListCount',
  /** 代办事项，返回造价咨询和招标代理的项目信息 */
  GetProAndPbaInfoToDoList: '/ProjectInfo/GetProAndPbaInfoToDoList',
  /** 报告逾期统计 */
  GetProBgyqCountList: '/ProjectInfo/GetProBgyqCountList',
  /** 导出报告逾期统计 */
  GetProBgyqCountListExcel: '/ProjectInfo/GetProBgyqCountListExcel',
  /** 初稿逾期统计（员工） */
  GetProCgyqCount: '/ProjectInfo/GetProCgyqCount',
  /** 导出初稿逾期统计-人员汇总 */
  GetProCgyqCountExcel: '/ProjectInfo/GetProCgyqCountExcel',
  /** 初稿逾期统计--项目详情 */
  GetProCgyqCountProDetail: '/ProjectInfo/GetProCgyqCountProDetail',
  /** 导出初稿逾期统计-项目详情 */
  GetProCgyqCountProDetailExcel: '/ProjectInfo/GetProCgyqCountProDetailExcel',
  /** 初稿逾期统计（员工）--人员详情 */
  GetProCgyqCountUserDetail: '/ProjectInfo/GetProCgyqCountUserDetail',
  /** 初稿逾期统计（员工）--人员详情统计 */
  GetProCgyqCountUserDetailCount: '/ProjectInfo/GetProCgyqCountUserDetailCount',
  /** 导出初稿逾期统计-人员详情 */
  GetProCgyqCountUserDetailExcel: '/ProjectInfo/GetProCgyqCountUserDetailExcel',
  /** 项目统计 */
  GetProCount: '/ProjectInfo/GetProCount',
  /** 获取项目文件模板 */
  GetProFileTemplateList: '/ProjectInfo/GetProFileTemplateList',
  /** 归档逾期统计 */
  GetProGdyqCountList: '/ProjectInfo/GetProGdyqCountList',
  /** 导出归档逾期统计 */
  GetProGdyqCountListExcel: '/ProjectInfo/GetProGdyqCountListExcel',
  /** 人员项目统计--造价咨询 */
  GetProPerProCountList: '/ProjectInfo/GetProPerProCountList',
  /** 人员项目统计--造价咨询相关人员 */
  GetProPerProDeatilList: '/ProjectInfo/GetProPerProDeatilList',
  /** 人员项目统计--造价咨询相关人员 */
  GetProPerProDeatilListCount: '/ProjectInfo/GetProPerProDeatilListCount',
  /** 获取项目信息列表--复核 */
  GetProjectInfoFhList: '/ProjectInfo/GetProjectInfoFhList',
  /** 获取项目信息列表 */
  GetProjectInfoList: '/ProjectInfo/GetProjectInfoList',
  /** 获取项目信息评列表Count */
  GetProjectInfoPiEstimateCount: '/ProjectInfo/GetProjectInfoPiEstimateCount',
  /** 获取项目信息评估列表 */
  GetProjectInfoPiEstimateList: '/ProjectInfo/GetProjectInfoPiEstimateList',
  /** 获取项目信息财审列表 */
  GetProjectInfoPiFinancialReviewList: '/ProjectInfo/GetProjectInfoPiFinancialReviewList',
  /** 获取项目信息财审列表Count */
  GetProjectInfoPiFinancialReviewListCount: '/ProjectInfo/GetProjectInfoPiFinancialReviewListCount',
  /** 获取项目信息税务列表 */
  GetProjectInfoPiTaxAdministrationList: '/ProjectInfo/GetProjectInfoPiTaxAdministrationList',
  /** 获取项目信息税务列表Count */
  GetProjectInfoPiTaxAdministrationListCount: '/ProjectInfo/GetProjectInfoPiTaxAdministrationListCount',
  /** 获取项目信息列表--待用户复核 */
  GetProjectInfoSecFhForUserList: '/ProjectInfo/GetProjectInfoSecFhForUserList',
  /** 项目底部汇总 */
  GetProjectInfoSum: '/ProjectInfo/GetProjectInfoSum',
  /** 造价三级复核-单人模式 */
  GetProjectInfoThFhForUserList: '/ProjectInfo/GetProjectInfoThFhForUserList',
  /** 获取项目列表--非视图 */
  GetProjectList: '/ProjectInfo/GetProjectList',
  /** 获取成员完成部分造价 */
  GetProjectUserWcPriceRate: '/ProjectInfo/GetProjectUserWcPriceRate',
  /** 返回项目数量统计 */
  GetProjectinfoAboutNum: '/ProjectInfo/GetProjectinfoAboutNum',
  /** 二级复核统计 */
  GetSecFhCountList: '/ProjectInfo/GetSecFhCountList',
  /** 二级复核统计 */
  GetSecFhCountListNew: '/ProjectInfo/GetSecFhCountListNew',
  /** 二级复核统计 */
  GetSecFhCountOldList: '/ProjectInfo/GetSecFhCountOldList',
  /** 获取单条造价咨询-评估-关系表 */
  GetSinglePiEstimate: '/ProjectInfo/GetSinglePiEstimate',
  /** 获取单条造价咨询-财审-关系表 */
  GetSinglePiFinancialReview: '/ProjectInfo/GetSinglePiFinancialReview',
  /** 获取单条造价咨询-税务-关系表 */
  GetSinglePiTaxAdministration: '/ProjectInfo/GetSinglePiTaxAdministration',
  /** 获取单条项目信息 */
  GetSingleProjectInfo: '/ProjectInfo/GetSingleProjectInfo',
  /** 获取单条项目业绩库--项目,整体项目,合同,相关单位信息 */
  GetSingleProjectInfoAchievement: '/ProjectInfo/GetSingleProjectInfoAchievement',
  /** 获取单条项目信息以及部门信息--合并接口 */
  GetSingleProjectInfoAndDep: '/ProjectInfo/GetSingleProjectInfoAndDep',
  /** 员工复核统计 */
  GetStaffFhCountList: '/ProjectInfo/GetStaffFhCountList',
  /** 员工复核统计 */
  GetStaffFhCountOldList: '/ProjectInfo/GetStaffFhCountOldList',
  /** 三级复核统计 */
  GetThFhCountList: '/ProjectInfo/GetThFhCountList',
  /** 三级复核统计 */
  GetThFhCountListNew: '/ProjectInfo/GetThFhCountListNew',
  /** 三级复核统计 */
  GetThFhCountOldList: '/ProjectInfo/GetThFhCountOldList',
  /** 人员复核结果统计 */
  GetUserFhResultCountList: '/ProjectInfo/GetUserFhResultCountList',
  /** 导出复核结果统计-人员 */
  GetUserFhResultCountListExcel: '/ProjectInfo/GetUserFhResultCountListExcel',
  /** 文字复核统计 */
  GetWzFhCountList: '/ProjectInfo/GetWzFhCountList',
  /** 作废项目 */
  IsCancelProjectInfo: '/ProjectInfo/IsCancelProjectInfo',
  /** 文档是否上传--归档 */
  ProFieGdJudge: '/ProjectInfo/ProFieGdJudge',
  /** 文档是否上传 */
  ProFieJudge: '/ProjectInfo/ProFieJudge',
  /** 项目归档提交 */
  ProFileComplete: '/ProjectInfo/ProFileComplete',
  /** 项目归档 */
  ProFileGd: '/ProjectInfo/ProFileGd',
  /** 项目归档--回退 */
  ProFileGdBack: '/ProjectInfo/ProFileGdBack',
  /** 项目归档接收 */
  ProFileJs: '/ProjectInfo/ProFileJs',
  /** 项目签发提交审批 */
  ProIssueSubmit: '/ProjectInfo/ProIssueSubmit',
  /** 项目经理--提交项目签发 */
  ProIssueSubmitByLead: '/ProjectInfo/ProIssueSubmitByLead',
  /** 问题是否回答 */
  ProQuestionJudge: '/ProjectInfo/ProQuestionJudge',
  /** 项目经理--提交三级复核 */
  ProThSubmitByLead: '/ProjectInfo/ProThSubmitByLead',
  /** 二级复核完成 */
  SecCheckComplete: '/ProjectInfo/SecCheckComplete',
  /** 二级复核提交(并更新预算价) */
  SecCheckSubmitWithYsPriceConfirm: '/ProjectInfo/SecCheckSubmitWithYsPriceConfirm',
  /** 预算金额变更-确认 */
  SecYsPriceUpdateConfirm: '/ProjectInfo/SecYsPriceUpdateConfirm',
  /** 预算金额变更-暂存 */
  SecYsPriceUpdateStaging: '/ProjectInfo/SecYsPriceUpdateStaging',
  /** 设置项目是否活跃 */
  SetProActive: '/ProjectInfo/SetProActive',
  /** 项目提交审批 */
  SubmitProjectInfo: '/ProjectInfo/SubmitProjectInfo',
  /** 提交盖章 */
  SubmitProjectSeal: '/ProjectInfo/SubmitProjectSeal',
  /** 三级复核分配 */
  ThCheckFp: '/ProjectInfo/ThCheckFp',
  /** 项目签发审批不通过 */
  UnExamineProIssue: '/ProjectInfo/UnExamineProIssue',
  /** 项目审批不通过 */
  UnExamineProjectInfo: '/ProjectInfo/UnExamineProjectInfo',
  /** 变更到第三方复核 */
  UpdateDsffh: '/ProjectInfo/UpdateDsffh',
  /** 修改项目信息 */
  UpdateProjectInfo: '/ProjectInfo/UpdateProjectInfo',
  /** 修改项目业绩库 */
  UpdateProjectInfoAchievement: '/ProjectInfo/UpdateProjectInfoAchievement',
  /** 修改项目报告号 */
  UpdateProjectInfoBgh: '/ProjectInfo/UpdateProjectInfoBgh',
  /** 修改项目信息--接收 */
  UpdateProjectInfoJs: '/ProjectInfo/UpdateProjectInfoJs',
  /** 打印暂存 */
  UpdateProjectInfoPrint: '/ProjectInfo/UpdateProjectInfoPrint',
  /** 修改是否外部复核字段 */
  UpdateProjectInfoWbFh: '/ProjectInfo/UpdateProjectInfoWbFh',
  /** 变更项目经理 */
  UpdateProjectLead: '/ProjectInfo/UpdateProjectLead',
  /** 修改项目信息--修改实际用章人 */
  UpdateProjectSjyz: '/ProjectInfo/UpdateProjectSjyz',
  /** 修改项目装订信息 */
  UpdateProjectZdInfo: '/ProjectInfo/UpdateProjectZdInfo',
  /** 文字复核打回--打回到文印 */
  WordsBackToSeal: '/ProjectInfo/WordsBackToSeal',
  /** 预算金额变更-确认 */
  YsPriceUpdateConfirm: '/ProjectInfo/YsPriceUpdateConfirm',
  /** 预算金额变更-暂存 */
  YsPriceUpdateStaging: '/ProjectInfo/YsPriceUpdateStaging',
}
export const apiAutoProjectInfoV2 = {
  /** 添加补充复核问题 */
  AddBcProjectQuestionV2: '/ProjectInfoV2/AddBcProjectQuestionV2',
  /** 添加复核问题 */
  AddProjectQuestionV2: '/ProjectInfoV2/AddProjectQuestionV2',
  /** 存档退回 */
  BackArchiveProjectInfoV2: '/ProjectInfoV2/BackArchiveProjectInfoV2',
  /** 打回一级复核 */
  BackFirstFhProjectInfoV2: '/ProjectInfoV2/BackFirstFhProjectInfoV2',
  /** 签发打回 */
  BackIssueProjectInfoV2: '/ProjectInfoV2/BackIssueProjectInfoV2',
  /** 打回二级复核 */
  BackSecFhProjectInfoV2: '/ProjectInfoV2/BackSecFhProjectInfoV2',
  /** 打回三级复核 */
  BackThFhProjectInfoV2: '/ProjectInfoV2/BackThFhProjectInfoV2',
  /** 批量添加复核问题 */
  BatchAddProjectQuestionV2: '/ProjectInfoV2/BatchAddProjectQuestionV2',
  /** 批量通过一级复核 */
  BatchPassFirstFhProjectInfoV2: '/ProjectInfoV2/BatchPassFirstFhProjectInfoV2',
  /** 通过二级复核 */
  BatchPassSecFhProjectInfoV2: '/ProjectInfoV2/BatchPassSecFhProjectInfoV2',
  /** 纠正复核问题 */
  CorrectProjectJsQuestion: '/ProjectInfoV2/CorrectProjectJsQuestion',
  /** 纠正复核问题 */
  CorrectionProjectQuestionV2: '/ProjectInfoV2/CorrectionProjectQuestionV2',
  /** 删除复核问题 */
  DeleteProjectQuestionV2: '/ProjectInfoV2/DeleteProjectQuestionV2',
  /** 进入一级复核 */
  EnterFirstFhProjectInfoV2: '/ProjectInfoV2/EnterFirstFhProjectInfoV2',
  /** 进入二级复核 */
  EnterSecFhProjectInfoV2: '/ProjectInfoV2/EnterSecFhProjectInfoV2',
  /** 审批打回 */
  ExamineBackProjectInfoV2: '/ProjectInfoV2/ExamineBackProjectInfoV2',
  /** 审批通过 */
  ExaminePassProjectInfoV2: '/ProjectInfoV2/ExaminePassProjectInfoV2',
  /** 导出报表生成 */
  ExportReportExcelProjectInfoV2: '/ProjectInfoV2/ExportReportExcelProjectInfoV2',
  /** 存档完成 */
  FinishArchiveProjectInfoV2: '/ProjectInfoV2/FinishArchiveProjectInfoV2',
  /** 复核人复核工作量 */
  GetCheckFhWorkloadProjectInfoListReProV2: '/ProjectInfoV2/GetCheckFhWorkloadProjectInfoListReProV2',
  /** 复核人复核工作量 */
  GetCheckFhWorkloadProjectInfoListV2: '/ProjectInfoV2/GetCheckFhWorkloadProjectInfoListV2',
  /** 获取一级复核列表 */
  GetFirstFhProjectInfoListV2: '/ProjectInfoV2/GetFirstFhProjectInfoListV2',
  /** 获取项目计数 */
  GetProjectGdTemplateList: '/ProjectInfoV2/GetProjectGdTemplateList',
  /** 获取项目计数 */
  GetProjectInfoCountV2: '/ProjectInfoV2/GetProjectInfoCountV2',
  /** 获取项目存档待归档列表 */
  GetProjectInfoGdDgdListV2: '/ProjectInfoV2/GetProjectInfoGdDgdListV2',
  /** 获取项目存档待接收列表 */
  GetProjectInfoGdDjsListV2: '/ProjectInfoV2/GetProjectInfoGdDjsListV2',
  /** 获取项目首页 */
  GetProjectInfoHomeV2: '/ProjectInfoV2/GetProjectInfoHomeV2',
  /** 获取项目列表 */
  GetProjectInfoListV2: '/ProjectInfoV2/GetProjectInfoListV2',
  /** 导出造价咨询 */
  GetProjectInfoListV2Excel: '/ProjectInfoV2/GetProjectInfoListV2Excel',
  /** 导出造价咨询台账 */
  GetProjectInfoListV2TjExcel: '/ProjectInfoV2/GetProjectInfoListV2TjExcel',
  /** 获取项目详情 */
  GetProjectInfoSingileV2: '/ProjectInfoV2/GetProjectInfoSingileV2',
  /** 获取项目总和、已出报告、未出报告 */
  GetProjectInfoSumV2: '/ProjectInfoV2/GetProjectInfoSumV2',
  /** 获取成员列表 */
  GetProjectMemberListV2: '/ProjectInfoV2/GetProjectMemberListV2',
  /** 获取质量控制流程单 */
  GetProjectQuestionForProExcelV2: '/ProjectInfoV2/GetProjectQuestionForProExcelV2',
  /** 获取复核问题列表 */
  GetProjectQuestionListV2: '/ProjectInfoV2/GetProjectQuestionListV2',
  /** 获取复核问题分类计数 */
  GetProjectQuestionMoIdCountV2: '/ProjectInfoV2/GetProjectQuestionMoIdCountV2',
  /** 获取复核问题阶段计数 */
  GetProjectQuestionTypeCountV2: '/ProjectInfoV2/GetProjectQuestionTypeCountV2',
  /** 获取二级复核列表 */
  GetSecFhProjectInfoListV2: '/ProjectInfoV2/GetSecFhProjectInfoListV2',
  /** 员工复核工作量 */
  GetStaffFhWorkloadProjectInfoListV2: '/ProjectInfoV2/GetStaffFhWorkloadProjectInfoListV2',
  /** 获取三级复核列表 */
  GetThFhProjectInfoListV2: '/ProjectInfoV2/GetThFhProjectInfoListV2',
  /** 获取待办列表 */
  GetTodoProjectInfoListV2: '/ProjectInfoV2/GetTodoProjectInfoListV2',
  /** 项目经理打回三级复核 */
  LeaderBackThFhProjectInfoV2: '/ProjectInfoV2/LeaderBackThFhProjectInfoV2',
  /** 通过一级复核 */
  PassFirstFhProjectInfoV2: '/ProjectInfoV2/PassFirstFhProjectInfoV2',
  /** 签发通过 */
  PassIssueProjectInfoV2: '/ProjectInfoV2/PassIssueProjectInfoV2',
  /** 通过二级复核 */
  PassSecFhProjectInfoV2: '/ProjectInfoV2/PassSecFhProjectInfoV2',
  /** 通过三级复核 */
  PassThFhProjectInfoV2: '/ProjectInfoV2/PassThFhProjectInfoV2',
  /** 存档接收 */
  ReceiveArchiveProjectInfoV2: '/ProjectInfoV2/ReceiveArchiveProjectInfoV2',
  /** 暂存和提交审批 */
  SaveProjectInfoV2: '/ProjectInfoV2/SaveProjectInfoV2',
  /** 设置上级复核人是否发现 */
  SetIsFhFoundProjectQuestionV2: '/ProjectInfoV2/SetIsFhFoundProjectQuestionV2',
  /** 更新复核问题是否扣分 */
  SetIsKfProjectQuestionV2: '/ProjectInfoV2/SetIsKfProjectQuestionV2',
  /** 更新复核问题是否统计 */
  SetIsTjProjectQuestionV2: '/ProjectInfoV2/SetIsTjProjectQuestionV2',
  /** 提交归档 */
  SubmitArchiveProjectInfoV2: '/ProjectInfoV2/SubmitArchiveProjectInfoV2',
  /** 提交一级复核 */
  SubmitFirstFhProjectInfoV2: '/ProjectInfoV2/SubmitFirstFhProjectInfoV2',
  /** 提交签发 */
  SubmitIssueProjectInfoV2: '/ProjectInfoV2/SubmitIssueProjectInfoV2',
  /** 通过二级复核 */
  SubmitSecFhProjectInfoV2: '/ProjectInfoV2/SubmitSecFhProjectInfoV2',
  /** 提交三级复核 */
  SubmitThFhProjectInfoV2: '/ProjectInfoV2/SubmitThFhProjectInfoV2',
  /** 提交盖章 */
  SubmitUsageSealProjectInfoV2: '/ProjectInfoV2/SubmitUsageSealProjectInfoV2',
  /** 同步核心系统项目 */
  SyncExtProjectInfo: '/ProjectInfoV2/SyncExtProjectInfo',
  /** 撤回一级复核通过 */
  UndoFirstFhPassProjectInfoV2: '/ProjectInfoV2/UndoFirstFhPassProjectInfoV2',
  /** 撤回一级复核提交 */
  UndoFirstFhSubmitProjectInfoV2: '/ProjectInfoV2/UndoFirstFhSubmitProjectInfoV2',
  /** 撤回二级复核通过 */
  UndoSecFhPassProjectInfoV2: '/ProjectInfoV2/UndoSecFhPassProjectInfoV2',
  /** 撤回二级复核提交 */
  UndoSecFhSubmitProjectInfoV2: '/ProjectInfoV2/UndoSecFhSubmitProjectInfoV2',
  /** 撤回三级复核提交 */
  UndoThFhPassProjectInfoV2: '/ProjectInfoV2/UndoThFhPassProjectInfoV2',
  /** 撤回三级复核提交 */
  UndoThFhSubmitProjectInfoV2: '/ProjectInfoV2/UndoThFhSubmitProjectInfoV2',
  /** 更新补充复核问题 */
  UpdateBcProjectQuestionV2: '/ProjectInfoV2/UpdateBcProjectQuestionV2',
  /** 变更复核人 */
  UpdateFhPerProjectUserV2: '/ProjectInfoV2/UpdateFhPerProjectUserV2',
  /** 更新项目信息 */
  UpdateProjectInfoV2: '/ProjectInfoV2/UpdateProjectInfoV2',
  /** 修改成员 */
  UpdateProjectMemberV2: '/ProjectInfoV2/UpdateProjectMemberV2',
  /** 更新复核问题 */
  UpdateProjectQuestionV2: '/ProjectInfoV2/UpdateProjectQuestionV2',
  /** 修改人员信息 */
  UpdateProjectUserV2: '/ProjectInfoV2/UpdateProjectUserV2',
  /** 修改人员二级复核人 */
  UpdateSecFhProjectUserV2: '/ProjectInfoV2/UpdateSecFhProjectUserV2',
  /** 更新三级复核人项目信息 */
  UpdateThFhProjectInfoV2: '/ProjectInfoV2/UpdateThFhProjectInfoV2',
  /** 文件下载 */
  ZipDownload: '/ProjectInfoV2/ZipDownload',
}
export const apiAutoProjectIrrigationWorks = {
  /** 新增水利工程统一口径 */
  AddProjectIrrigationWorks: '/ProjectIrrigationWorks/AddProjectIrrigationWorks',
  /** 删除水利工程统一口径 */
  DeleteProjectIrrigationWorks: '/ProjectIrrigationWorks/DeleteProjectIrrigationWorks',
  /** 获取水利工程统一口径 */
  GetProjectIrrigationWorksList: '/ProjectIrrigationWorks/GetProjectIrrigationWorksList',
  /** 获取水利工程统一口径--单条 */
  GetProjectIrrigationWorksSingle: '/ProjectIrrigationWorks/GetProjectIrrigationWorksSingle',
  /** 修改水利工程统一口径 */
  UpdateProjectIrrigationWorks: '/ProjectIrrigationWorks/UpdateProjectIrrigationWorks',
}
export const apiAutoProjectPrintRecord = {
  /** 新增项目打印记录 */
  AddProjectPrintRecord: '/ProjectPrintRecord/AddProjectPrintRecord',
  /** 删除项目打印记录 */
  DeleteProjectPrintRecord: '/ProjectPrintRecord/DeleteProjectPrintRecord',
  /** 获取项目打印记录列表 */
  GetProjectPrintRecordList: '/ProjectPrintRecord/GetProjectPrintRecordList',
  /** 获取项目打印记录--单条 */
  GetProjectPrintRecordSingle: '/ProjectPrintRecord/GetProjectPrintRecordSingle',
  /** 修改项目打印记录 */
  UpdateProjectPrintRecord: '/ProjectPrintRecord/UpdateProjectPrintRecord',
}
export const apiAutoProjectQuestion = {
  /** 新增项目问题 */
  AddProjectQuestion: '/ProjectQuestion/AddProjectQuestion',
  /** 复制项目问题 */
  CopyProjectQuestion: '/ProjectQuestion/CopyProjectQuestion',
  /** 删除项目问题 */
  DeleteProjectQuestion: '/ProjectQuestion/DeleteProjectQuestion',
  /** 导出复核问题月报 */
  ExportProjectQuestion: '/ProjectQuestion/ExportProjectQuestion',
  /** 统计复核问题数量 */
  GetProQuestionCount: '/ProjectQuestion/GetProQuestionCount',
  /** 获取项目问题列表-所有 */
  GetProjectQuestionAllList: '/ProjectQuestion/GetProjectQuestionAllList',
  /** 统计复核问题数量--按模板分组 */
  GetProjectQuestionGroupCount: '/ProjectQuestion/GetProjectQuestionGroupCount',
  /** 获取项目问题列表 */
  GetProjectQuestionList: '/ProjectQuestion/GetProjectQuestionList',
  /** 统计复核问题数量--按类型 */
  GetProjectQuestionSummaryCount: '/ProjectQuestion/GetProjectQuestionSummaryCount',
  /** 获取项目问题汇总列表 */
  GetProjectQuestionSummaryList: '/ProjectQuestion/GetProjectQuestionSummaryList',
  /** 问题是否填写完整 */
  ProjectQuestionJudge: '/ProjectQuestion/ProjectQuestionJudge',
  /** 修改项目问题 */
  UpdateProjectQuestion: '/ProjectQuestion/UpdateProjectQuestion',
  /** 批量修改项目问题 */
  UpdateProjectQuestionBath: '/ProjectQuestion/UpdateProjectQuestionBath',
  /** 项目问题扣分 */
  UpdateProjectQuestionKf: '/ProjectQuestion/UpdateProjectQuestionKf',
  /** 项目问题不扣分 */
  UpdateProjectQuestionNotKf: '/ProjectQuestion/UpdateProjectQuestionNotKf',
  /** 项目问题不统计 */
  UpdateProjectQuestionNotTj: '/ProjectQuestion/UpdateProjectQuestionNotTj',
  /** 项目问题需要统计 */
  UpdateProjectQuestionTj: '/ProjectQuestion/UpdateProjectQuestionTj',
}
export const apiAutoProjectSeal = {
  /** 新增项目用章 */
  AddProjectSeal: '/ProjectSeal/AddProjectSeal',
  /** 删除项目用章 */
  DeleteProjectSeal: '/ProjectSeal/DeleteProjectSeal',
  /** 项目用章审批通过 */
  ExamineProjectSeal: '/ProjectSeal/ExamineProjectSeal',
  /** 获取项目用章列表 */
  GetProjectSealList: '/ProjectSeal/GetProjectSealList',
  /** 获取项目用章列表统计 */
  GetProjectSealListCount: '/ProjectSeal/GetProjectSealListCount',
  /** 获取项目用章单条 */
  GetProjectSealSingle: '/ProjectSeal/GetProjectSealSingle',
  /** 返回是否可用数量 */
  ProjectSealIsUserfulCount: '/ProjectSeal/ProjectSealIsUserfulCount',
  /** 项目用章提交审批 */
  ProjectSealSubmit: '/ProjectSeal/ProjectSealSubmit',
  /** 设置项目用章是否可用 */
  SetProjectSealIsUserful: '/ProjectSeal/SetProjectSealIsUserful',
  /** 项目用章审批不通过 */
  UnExamineProjectSeal: '/ProjectSeal/UnExamineProjectSeal',
  /** 修改项目用章 */
  UpdateProjectSeal: '/ProjectSeal/UpdateProjectSeal',
}
export const apiAutoProjectSettlement = {
  /** 新增结算审批统一口径 */
  AddProjectSettlement: '/ProjectSettlement/AddProjectSettlement',
  /** 删除结算审批统一口径 */
  DeleteProjectSettlement: '/ProjectSettlement/DeleteProjectSettlement',
  /** 获取结算审批统一口径 */
  GetProjectSettlementList: '/ProjectSettlement/GetProjectSettlementList',
  /** 获取结算审批统一口径--单条 */
  GetProjectSettlementSingle: '/ProjectSettlement/GetProjectSettlementSingle',
  /** 修改结算审批统一口径 */
  UpdateProjectSettlement: '/ProjectSettlement/UpdateProjectSettlement',
}
export const apiAutoProjectUniformCaliber = {
  /** 获取统一口径待完善列表 */
  GetProjectUniformCaliberList: '/ProjectUniformCaliber/GetProjectUniformCaliberList',
}
export const apiAutoProjectUpdate = {
  /** 新增项目变更 */
  AddProjectUpdate: '/ProjectUpdate/AddProjectUpdate',
  /** 删除项目变更 */
  DeleteProjectUpdate: '/ProjectUpdate/DeleteProjectUpdate',
  /** 审批通过项目变更 */
  ExamineProjectUpdate: '/ProjectUpdate/ExamineProjectUpdate',
  /** 项目变更统计 */
  GetProUpdateCount: '/ProjectUpdate/GetProUpdateCount',
  /** 获取项目变更列表 */
  GetProjectUpdateList: '/ProjectUpdate/GetProjectUpdateList',
  /** 获取项目变更--单条 */
  GetProjectUpdateSingle: '/ProjectUpdate/GetProjectUpdateSingle',
  /** 作废项目变更 */
  IsCancelProjectUpdate: '/ProjectUpdate/IsCancelProjectUpdate',
  /** 项目变更撤回判断 */
  ProReCallJudge: '/ProjectUpdate/ProReCallJudge',
  /** 项目变更撤回 */
  ProjectUpdateReCall: '/ProjectUpdate/ProjectUpdateReCall',
  /** 项目变更提交审批 */
  ProjectUpdateSubmit: '/ProjectUpdate/ProjectUpdateSubmit',
  /** 审批不通过项目变更 */
  UnExamineProjectUpdate: '/ProjectUpdate/UnExamineProjectUpdate',
  /** 修改项目变更 */
  UpdateProjectUpdate: '/ProjectUpdate/UpdateProjectUpdate',
}
export const apiAutoProjectUpdateV2 = {
  /** 审批打回 */
  BackExamineProjectUpdateV2: '/ProjectUpdateV2/BackExamineProjectUpdateV2',
  /** 删除 */
  DeleteProjectUpdateV2: '/ProjectUpdateV2/DeleteProjectUpdateV2',
  /** 变更状态列表 */
  GetProjectUpdateV2Count: '/ProjectUpdateV2/GetProjectUpdateV2Count',
  /** 变更列表 */
  GetProjectUpdateV2List: '/ProjectUpdateV2/GetProjectUpdateV2List',
  /** 审批通过 */
  PassExamineProjectUpdateV2: '/ProjectUpdateV2/PassExamineProjectUpdateV2',
  /** 撤回 */
  ProjectUpdateReCallV2: '/ProjectUpdateV2/ProjectUpdateReCallV2',
  /** 暂存和提交审批 */
  SubmitProjectUpdateV2: '/ProjectUpdateV2/SubmitProjectUpdateV2',
}
export const apiAutoProjectUsageSeal = {
  /** 新增用章 */
  AddProjectUsageSeal: '/ProjectUsageSeal/AddProjectUsageSeal',
  /** 删除用章 */
  DeleteProjectUsageSeal: '/ProjectUsageSeal/DeleteProjectUsageSeal',
  /** 审批通过用章单 */
  ExamineProjectUsageSeal: '/ProjectUsageSeal/ExamineProjectUsageSeal',
  /** 用章统计 */
  GetProjectUsageSealCount: '/ProjectUsageSeal/GetProjectUsageSealCount',
  /** 获取用章列表首页 */
  GetProjectUsageSealIndexList: '/ProjectUsageSeal/GetProjectUsageSealIndexList',
  /** 获取用章列表 */
  GetProjectUsageSealList: '/ProjectUsageSeal/GetProjectUsageSealList',
  /** 导出 */
  GetProjectUsageSealListExcel: '/ProjectUsageSeal/GetProjectUsageSealListExcel',
  /** 获取用章--单条 */
  GetProjectUsageSealSingle: '/ProjectUsageSeal/GetProjectUsageSealSingle',
  /** 通过管理获取用章 */
  GetProjectUsageSealSingleByRe: '/ProjectUsageSeal/GetProjectUsageSealSingleByRe',
  /** 获取关联的合同 */
  GetReProjectContract: '/ProjectUsageSeal/GetReProjectContract',
  /** 项目章-用章盖章 */
  GzProjectSeal: '/ProjectUsageSeal/GzProjectSeal',
  /** 用章盖章 */
  GzProjectUsageSeal: '/ProjectUsageSeal/GzProjectUsageSeal',
  /** 用章盖章--批量 */
  GzProjectUsageSealBath: '/ProjectUsageSeal/GzProjectUsageSealBath',
  /** 项目章-盖章不通过 */
  GzUnExamineProjectSeal: '/ProjectUsageSeal/GzUnExamineProjectSeal',
  /** 盖章不通过 */
  GzUnExamineProjectUsageSeal: '/ProjectUsageSeal/GzUnExamineProjectUsageSeal',
  /** 作废用章单 */
  IsCancelProjectUsageSeal: '/ProjectUsageSeal/IsCancelProjectUsageSeal',
  /** 用章重新提交审批 */
  ProjectUsageSealAgainSubmit: '/ProjectUsageSeal/ProjectUsageSealAgainSubmit',
  /** 用章提交审批 */
  ProjectUsageSealSubmit: '/ProjectUsageSeal/ProjectUsageSealSubmit',
  /** 审批不通过 */
  UnExamineProjectUsageSeal: '/ProjectUsageSeal/UnExamineProjectUsageSeal',
  /** 修改用章 */
  UpdateProjectUsageSeal: '/ProjectUsageSeal/UpdateProjectUsageSeal',
}
export const apiAutoProjectUser = {
  /** 新增项目成员 */
  AddProjectUser: '/ProjectUser/AddProjectUser',
  /** 新增项目成员--批量 */
  AddProjectUserBath: '/ProjectUser/AddProjectUserBath',
  /** 批量判断二级复核是否全完完成判断 */
  BatchJudgeSecFhIsCompleteFinish: '/ProjectUser/BatchJudgeSecFhIsCompleteFinish',
  /** 批量判断三级复核是否全完完成判断 */
  BatchJudgeThFhIsCompleteFinishByProId: '/ProjectUser/BatchJudgeThFhIsCompleteFinishByProId',
  /** 二级复核批量通过 */
  BatchSecCheckEamine: '/ProjectUser/BatchSecCheckEamine',
  /** 三级复核批量通过 */
  BatchThCheckEamine: '/ProjectUser/BatchThCheckEamine',
  /** 初稿完成 */
  CgComplete: '/ProjectUser/CgComplete',
  /** 删除项目成员 */
  DeleteProjectUser: '/ProjectUser/DeleteProjectUser',
  /** 导出财务审计项目业绩库 */
  ExportCwsjProjectUserWithAchievement: '/ProjectUser/ExportCwsjProjectUserWithAchievement',
  /** 导出前期咨询项目业绩库 */
  ExportQqzxProjectUserWithAchievement: '/ProjectUser/ExportQqzxProjectUserWithAchievement',
  /** 导出税务审计项目业绩库 */
  ExportSwsjProjectUserWithAchievement: '/ProjectUser/ExportSwsjProjectUserWithAchievement',
  /** 导出资产评估项目业绩库 */
  ExportZcpgProjectUserWithAchievement: '/ProjectUser/ExportZcpgProjectUserWithAchievement',
  /** 导出造价咨询项目业绩库 */
  ExportZjzxProjectUserWithAchievementList: '/ProjectUser/ExportZjzxProjectUserWithAchievementList',
  /** 获取项目成员列表 */
  GetProjectUserList: '/ProjectUser/GetProjectUserList',
  /** 获取项目成员列表（业绩库） */
  GetProjectUserWithAchievementList: '/ProjectUser/GetProjectUserWithAchievementList',
  /** 判断项目二级复核是否全部通过 */
  GetSecFhIsFinish: '/ProjectUser/GetSecFhIsFinish',
  /** 获取单条项目成员 */
  GetSingleProjectUser: '/ProjectUser/GetSingleProjectUser',
  /** 获取项目成员对应项目信息 */
  GetVProjectUserFpList: '/ProjectUser/GetVProjectUserFpList',
  /** 判断能否初稿完成 */
  JudgeCgComplete: '/ProjectUser/JudgeCgComplete',
  /** 判断能否提交二级复核 */
  JudgeSecCheckSubmit: '/ProjectUser/JudgeSecCheckSubmit',
  /** 二级复核是否全完完成判断 */
  JudgeSecFhIsCompleteFinish: '/ProjectUser/JudgeSecFhIsCompleteFinish',
  /** 三级复核是否全完完成判断 */
  JudgeThFhIsCompleteFinish: '/ProjectUser/JudgeThFhIsCompleteFinish',
  /** 判断该部门是否有专业负责人 */
  ProUserZyJudge: '/ProjectUser/ProUserZyJudge',
  /** 二级复核打回 */
  SecCheckBack: '/ProjectUser/SecCheckBack',
  /** 二级复核审批通过 */
  SecCheckEamine: '/ProjectUser/SecCheckEamine',
  /** 二级复核--撤回 */
  SecCheckRecall: '/ProjectUser/SecCheckRecall',
  /** 二级复核提交 */
  SecCheckSubmit: '/ProjectUser/SecCheckSubmit',
  /** 二级复核提交--打回 */
  SecCheckSubmitBack: '/ProjectUser/SecCheckSubmitBack',
  /** 二级复核撤回 */
  SecFhBack: '/ProjectUser/SecFhBack',
  /** 三级复核打回 */
  ThCheckBack: '/ProjectUser/ThCheckBack',
  /** 三级复核审批通过 */
  ThCheckEamine: '/ProjectUser/ThCheckEamine',
  /** 三级复核撤回 */
  ThCheckRecall: '/ProjectUser/ThCheckRecall',
  /** 三级复核提交 */
  ThCheckSubmit: '/ProjectUser/ThCheckSubmit',
  /** 三级复核提交、并更新预算价 */
  ThCheckSubmitWithYsPriceConfirm: '/ProjectUser/ThCheckSubmitWithYsPriceConfirm',
  /** 三级复核撤回 */
  ThFhBack: '/ProjectUser/ThFhBack',
  /** 修改项目成员 */
  UpdateProjectUser: '/ProjectUser/UpdateProjectUser',
  /** 终稿完成 */
  ZgComplete: '/ProjectUser/ZgComplete',
  /** 自检 */
  ZjProjectUser: '/ProjectUser/ZjProjectUser',
  /** 自评分完成 */
  ZpfComplete: '/ProjectUser/ZpfComplete',
}
export const apiAutoProjectUserPerformance = {
  /** 添加项目成员绩效 */
  AddProjectUserPerformance: '/ProjectUserPerformance/AddProjectUserPerformance',
  /** 计算项目成员绩效 */
  BatchCalcProjectUserPerformance: '/ProjectUserPerformance/BatchCalcProjectUserPerformance',
  /** 批量作废项目成员绩效 */
  BatchCancelProjectUserPerformance: '/ProjectUserPerformance/BatchCancelProjectUserPerformance',
  /** 更新项目成员绩效 */
  BatchUpdateProjectUserPerformance: '/ProjectUserPerformance/BatchUpdateProjectUserPerformance',
  /** 计算项目成员绩效 */
  CalcAll: '/ProjectUserPerformance/CalcAll',
  /** 计算项目成员绩效 */
  CalcProjectUserPerformance: '/ProjectUserPerformance/CalcProjectUserPerformance',
  /** 删除项目成员绩效 */
  DeleteProjectUserPerformance: '/ProjectUserPerformance/DeleteProjectUserPerformance',
  /** 获取项目成员绩效Excel */
  GetProjectUserPerformancExcel: '/ProjectUserPerformance/GetProjectUserPerformancExcel',
  /** 获取项目成员绩效默认值 */
  GetProjectUserPerformanceDefaultValue: '/ProjectUserPerformance/GetProjectUserPerformanceDefaultValue',
  /** 造价咨询统计-复核人员导出 */
  GetProjectUserPerformanceExcelByProjectFhUser: '/ProjectUserPerformance/GetProjectUserPerformanceExcelByProjectFhUser',
  /** 造价咨询统计-作业人员导出 */
  GetProjectUserPerformanceExcelByProjectUser: '/ProjectUserPerformance/GetProjectUserPerformanceExcelByProjectUser',
  /** 获取项目成员绩效列表 */
  GetProjectUserPerformanceList: '/ProjectUserPerformance/GetProjectUserPerformanceList',
  /** 获取项目成员绩效列表下拉框（项目内） */
  GetProjectUserPerformanceSelect: '/ProjectUserPerformance/GetProjectUserPerformanceSelect',
  /** 造价咨询统计-全部（以部门为主体） */
  GetProjectUserPerformanceStatisticsByDep: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByDep',
  /** 造价咨询统计-全部（以部门为主体） */
  GetProjectUserPerformanceStatisticsByDepSummary: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByDepSummary',
  /** 造价咨询统计-部门（以本部门成员为主体） */
  GetProjectUserPerformanceStatisticsByPer: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByPer',
  /** 造价咨询统计-部门（以本部门成员为主体） */
  GetProjectUserPerformanceStatisticsByPerSummary: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByPerSummary',
  /** 造价咨询统计-全部（以项目为主体） */
  GetProjectUserPerformanceStatisticsByPro: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByPro',
  /** 造价咨询统计-全部（以项目为主体）合计 */
  GetProjectUserPerformanceStatisticsByProSummary: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsByProSummary',
  /** 造价咨询统计-全部（以部门为主体） */
  GetProjectUserPerformanceStatisticsExcelByDep: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsExcelByDep',
  /** 造价咨询统计-部门（以本部门成员为主体） */
  GetProjectUserPerformanceStatisticsExcelByPer: '/ProjectUserPerformance/GetProjectUserPerformanceStatisticsExcelByPer',
  /** 获取项目成员绩效列表汇总 */
  GetProjectUserPerformanceSummary: '/ProjectUserPerformance/GetProjectUserPerformanceSummary',
  /** 同步项目成员绩效 */
  SyncProjectUserPerformance: '/ProjectUserPerformance/SyncProjectUserPerformance',
  /** 同步项目效益调整 */
  SyncXytzProjectUserPerformance: '/ProjectUserPerformance/SyncXytzProjectUserPerformance',
  /** 更新项目成员绩效 */
  UpdateProjectUserPerformance: '/ProjectUserPerformance/UpdateProjectUserPerformance',
}
export const apiAutoPsOperationLog = {
  /** 获取项目章操作日志 */
  GetPsOperationLogList: '/PsOperationLog/GetPsOperationLogList',
}
export const apiAutoQccApi = {
  /** 企查查--企业工商信息查询 */
  GetBasicDetailsByName: '/QccApi/GetBasicDetailsByName',
  /** 企查查--查询企业名称 */
  GetCompanyNameByQcc: '/QccApi/GetCompanyNameByQcc',
  /** 同步企查查--企业工商信息 */
  synchronizationBasicDetailsByName: '/QccApi/synchronizationBasicDetailsByName',
}
export const apiAutoQgcMilepost = {
  /** 新增里程碑管理 */
  AddQgcMilepost: '/QgcMilepost/AddQgcMilepost',
  /** 删除里程碑管理 */
  DeleteQgcMilepost: '/QgcMilepost/DeleteQgcMilepost',
  /** 获取里程碑管理列表 */
  GetQgcMilepostList: '/QgcMilepost/GetQgcMilepostList',
  /** 修改里程碑管理 */
  UpdateQgcMilepost: '/QgcMilepost/UpdateQgcMilepost',
}
export const apiAutoQgczxProject = {
  /** 新增全过程咨询项目 */
  AddQgczxProject: '/QgczxProject/AddQgczxProject',
  /** 删除全过程咨询项目 */
  DeleteQgczxProject: '/QgczxProject/DeleteQgczxProject',
  /** 全过程咨询审批通过 */
  ExamineQgczxProject: '/QgczxProject/ExamineQgczxProject',
  /** 导出自定义地址全过程咨询统计 */
  ExportCusAddressQgczxProjectList: '/QgczxProject/ExportCusAddressQgczxProjectList',
  /** 导出全过程咨询项目列表--业绩库相关 */
  ExportQgczxProjectAchievement: '/QgczxProject/ExportQgczxProjectAchievement',
  /** 导出保证金记录 */
  ExportQgczxProjectList: '/QgczxProject/ExportQgczxProjectList',
  /** 导出全过程咨询员工业绩库 */
  ExportQgczxProjectUserWithAchievement: '/QgczxProject/ExportQgczxProjectUserWithAchievement',
  /** 获取全过程咨询项目列表--业绩库相关 */
  GetQgczxProjectAchievementList: '/QgczxProject/GetQgczxProjectAchievementList',
  /** 获取全过程咨询项目项目概况iframe路由地址 */
  GetQgczxProjectAiRouteAddress: '/QgczxProject/GetQgczxProjectAiRouteAddress',
  /** 获取全过程咨询项目-跟踪审计项目--单条 */
  GetQgczxProjectAndAuditItemSingle: '/QgczxProject/GetQgczxProjectAndAuditItemSingle',
  /** 获取全过程咨询项目列表 */
  GetQgczxProjectList: '/QgczxProject/GetQgczxProjectList',
  /** 获取全过程咨询项目列表统计-状态计数 */
  GetQgczxProjectListCount: '/QgczxProject/GetQgczxProjectListCount',
  /** 获取全过程咨询项目列表统计-计数 */
  GetQgczxProjectListSum: '/QgczxProject/GetQgczxProjectListSum',
  /** 获取全过程咨询项目--单条 */
  GetQgczxProjectSingle: '/QgczxProject/GetQgczxProjectSingle',
  /** 全过程咨询综合统计 */
  GetQgczxProjectSynthesizeCount: '/QgczxProject/GetQgczxProjectSynthesizeCount',
  /** 获取跟踪审计员工业绩库 */
  GetQgczxProjectUserWithAchievementList: '/QgczxProject/GetQgczxProjectUserWithAchievementList',
  /** 获取单条项目业绩库--项目,整体项目,合同,相关单位信息 */
  GetSingleQgczxProjectAchievement: '/QgczxProject/GetSingleQgczxProjectAchievement',
  /** 作废全过程咨询项目 */
  IsCancelQgczxProject: '/QgczxProject/IsCancelQgczxProject',
  /** 全过程咨询 统计 */
  QgczCount: '/QgczxProject/QgczCount',
  /** 全过程咨询提交审批 */
  QgczxProjectSubmit: '/QgczxProject/QgczxProjectSubmit',
  /** 设置全过程咨询项目是否活跃 */
  SetQgczxProActive: '/QgczxProject/SetQgczxProActive',
  /** 全过程咨询审批不通过 */
  UnExamineQgczxProject: '/QgczxProject/UnExamineQgczxProject',
  /** 管理员结束项目 */
  UpdateProManageEndState: '/QgczxProject/UpdateProManageEndState',
  /** 修改全过程咨询项目PmUrl,PmRouteAddress,PmDisplayItemConfigJson */
  UpdateProManagePmUrlNew: '/QgczxProject/UpdateProManagePmUrlNew',
  /** 管理员恢复项目 */
  UpdateProManageStartState: '/QgczxProject/UpdateProManageStartState',
  /** 修改全过程咨询项目 */
  UpdateQgczxProject: '/QgczxProject/UpdateQgczxProject',
  /** 修改项目业绩库 */
  UpdateQgczxProjectAchievement: '/QgczxProject/UpdateQgczxProjectAchievement',
  /** 修改全过程咨询项目外链 */
  UpdateQgczxProjectAiUrl: '/QgczxProject/UpdateQgczxProjectAiUrl',
  /** 更新监理手册模板数据 */
  UpdateQgczxProjectSmtData: '/QgczxProject/UpdateQgczxProjectSmtData',
}
export const apiAutoQgczxProjectCost = {
  /** 添加人员获取成本 */
  AddQgczxProjectCost: '/QgczxProjectCost/AddQgczxProjectCost',
  /** 删除人员获取成本 */
  DeleteQgczxProjectCost: '/QgczxProjectCost/DeleteQgczxProjectCost',
  /** 项目创收及开票统计(单个项目) */
  ExportQgczxJlPerformance: '/QgczxProjectCost/ExportQgczxJlPerformance',
  /** 项目创收及开票统计(单个项目) */
  ExportQgczxZjPerformance: '/QgczxProjectCost/ExportQgczxZjPerformance',
  /** 项目创收及开票统计(单个项目) */
  ExportQgczxZxPerformance: '/QgczxProjectCost/ExportQgczxZxPerformance',
  /** 项目人力成本统计(单个项目) */
  GetQgczxProjectCostDateSummary: '/QgczxProjectCost/GetQgczxProjectCostDateSummary',
  /** 获取人员获取成本 */
  GetQgczxProjectCostList: '/QgczxProjectCost/GetQgczxProjectCostList',
  /** 获取人员获取成本(汇总) */
  GetQgczxProjectCostListSummary: '/QgczxProjectCost/GetQgczxProjectCostListSummary',
  /** 获取项目成本汇总列表(全局) */
  GetQgczxProjectCostSummaryList: '/QgczxProjectCost/GetQgczxProjectCostSummaryList',
  /** 获取项目成本汇总列表汇总(全局) */
  GetQgczxProjectCostSummaryListSummary: '/QgczxProjectCost/GetQgczxProjectCostSummaryListSummary',
  /** 项目创收及开票统计(单个项目) */
  GetQgczxProjectCreateIncomeWithInvoice: '/QgczxProjectCost/GetQgczxProjectCreateIncomeWithInvoice',
  /** 项目借款金额统计(单个项目) */
  GetQgczxProjectLoanInfoTypeSummary: '/QgczxProjectCost/GetQgczxProjectLoanInfoTypeSummary',
  /** 获取成本汇总统计(全局) */
  GetQgczxProjectReimburseTypeSummary: '/QgczxProjectCost/GetQgczxProjectReimburseTypeSummary',
  /** 获取项目成本汇总单个项目(全局) */
  GetSingleQgczxProjectCostSummary: '/QgczxProjectCost/GetSingleQgczxProjectCostSummary',
  /** 获取项目成本汇总合计(全局) */
  GetlQgczxProjectCostSummary: '/QgczxProjectCost/GetlQgczxProjectCostSummary',
  /** 更新人员获取成本 */
  UpdateQgczxProjectCost: '/QgczxProjectCost/UpdateQgczxProjectCost',
}
export const apiAutoQgczxProjectPlan = {
  /** 新增全过程项目计划 */
  AddQgczxProjectPlan: '/QgczxProjectPlan/AddQgczxProjectPlan',
  /** 删除全过程项目计划 */
  DeleteQgczxProjectPlan: '/QgczxProjectPlan/DeleteQgczxProjectPlan',
  /** 获取全过程项目计划列表 */
  GetQgczxProjectPlanList: '/QgczxProjectPlan/GetQgczxProjectPlanList',
  /** 修改全过程项目计划 */
  UpdateQgczxProjectPlan: '/QgczxProjectPlan/UpdateQgczxProjectPlan',
}
export const apiAutoQgczxProjectReport = {
  /** 新增全过程咨询项目报告 */
  AddQgczxProjectReport: '/QgczxProjectReport/AddQgczxProjectReport',
  /** 作废或取消全过程咨询项目报告 */
  CancelOrNotQgczxProjectReport: '/QgczxProjectReport/CancelOrNotQgczxProjectReport',
  /** 删除全过程咨询项目报告 */
  DeleteQgczxProjectReport: '/QgczxProjectReport/DeleteQgczxProjectReport',
  /** 全过程项目报告审批通过 */
  ExamineQgczxProjectReport: '/QgczxProjectReport/ExamineQgczxProjectReport',
  /** 获取全过程咨询项目报告列表 */
  GetQgczxProjectReportList: '/QgczxProjectReport/GetQgczxProjectReportList',
  /** 获取全过程咨询项目报告Count */
  GetQgczxProjectReportListCount: '/QgczxProjectReport/GetQgczxProjectReportListCount',
  /** 获取全过程咨询项目报告单条 */
  GetQgczxProjectReportSingle: '/QgczxProjectReport/GetQgczxProjectReportSingle',
  /** 获取全过程咨询项目报告类型统计 */
  GetQgczxProjectReportTypeCount: '/QgczxProjectReport/GetQgczxProjectReportTypeCount',
  /** 全过程项目报告提交审批 */
  QgczxProjectReportSubmit: '/QgczxProjectReport/QgczxProjectReportSubmit',
  /** 全过程项目报告审批不通过 */
  UnExamineQgczxProjectReport: '/QgczxProjectReport/UnExamineQgczxProjectReport',
  /** 修改全过程咨询项目报告 */
  UpdateQgczxProjectReport: '/QgczxProjectReport/UpdateQgczxProjectReport',
}
export const apiAutoQgczxProjectUpdate = {
  /** 新增跟踪审计项目变更 */
  AddQgczxProjectUpdate: '/QgczxProjectUpdate/AddQgczxProjectUpdate',
  /** 删除跟踪审计项目变更 */
  DeleteQgczxProjectUpdate: '/QgczxProjectUpdate/DeleteQgczxProjectUpdate',
  /** 跟踪审计项目项目变更审批通过 */
  ExamineQgczxProjectUpdate: '/QgczxProjectUpdate/ExamineQgczxProjectUpdate',
  /** 获取跟踪审计项目变更列表 */
  GetQgczxProjectUpdateList: '/QgczxProjectUpdate/GetQgczxProjectUpdateList',
  /** 获取跟踪审计项目变更Count */
  GetQgczxProjectUpdateListCount: '/QgczxProjectUpdate/GetQgczxProjectUpdateListCount',
  /** 删除跟踪审计项目变更 */
  GetQgczxProjectUpdateSingle: '/QgczxProjectUpdate/GetQgczxProjectUpdateSingle',
  /** 是否作废跟踪审计项目项目变更 */
  IsCancelQgczxProjectUpdate: '/QgczxProjectUpdate/IsCancelQgczxProjectUpdate',
  /** 判断能否撤回 */
  ProReCallJudge: '/QgczxProjectUpdate/ProReCallJudge',
  /** 跟踪审计项目项目变更撤回 */
  QgczxProjectUpdateReCall: '/QgczxProjectUpdate/QgczxProjectUpdateReCall',
  /** 跟踪审计项目项目变更提交审批 */
  QgczxProjectUpdateSubmit: '/QgczxProjectUpdate/QgczxProjectUpdateSubmit',
  /** 跟踪审计项目项目变更审批不通过 */
  UnExamineQgczxProjectUpdate: '/QgczxProjectUpdate/UnExamineQgczxProjectUpdate',
  /** 修改跟踪审计项目变更 */
  UpdateQgczxProjectUpdate: '/QgczxProjectUpdate/UpdateQgczxProjectUpdate',
}
export const apiAutoQgczxWorkMajorDecisions = {
  /** 新增项管工作重大决策 */
  AddQgczxWorkMajorDecisions: '/QgczxWorkMajorDecisions/AddQgczxWorkMajorDecisions',
  /** 删除项管工作重大决策 */
  DeleteQgczxWorkMajorDecisions: '/QgczxWorkMajorDecisions/DeleteQgczxWorkMajorDecisions',
  /** 获取项管工作重大决策列表 */
  GetQgczxWorkMajorDecisionsList: '/QgczxWorkMajorDecisions/GetQgczxWorkMajorDecisionsList',
  /** 获取项管工作重大决策列表 */
  GetQgczxWorkMajorDecisionsSingle: '/QgczxWorkMajorDecisions/GetQgczxWorkMajorDecisionsSingle',
  /** 是否作废项管工作重大决策 */
  IsCancelQgczxWorkMajorDecisions: '/QgczxWorkMajorDecisions/IsCancelQgczxWorkMajorDecisions',
  /** 修改项管工作重大决策 */
  UpdateQgczxWorkMajorDecisions: '/QgczxWorkMajorDecisions/UpdateQgczxWorkMajorDecisions',
}
export const apiAutoQuestionCategory = {
  /** 新增问题分类 */
  AddQuestionCategory: '/QuestionCategory/AddQuestionCategory',
  /** 删除问题分类 */
  DeleteQuestionCategory: '/QuestionCategory/DeleteQuestionCategory',
  /** 获取问题分类列表 */
  GetQuestionCategoryList: '/QuestionCategory/GetQuestionCategoryList',
  /** 修改问题分类 */
  UpdateQuestionCategory: '/QuestionCategory/UpdateQuestionCategory',
}
export const apiAutoReasonableQuestion = {
  /** 新增合理性提问信息 */
  AddReasonableQuestion: '/ReasonableQuestion/AddReasonableQuestion',
  /** 作废合理性提问信息 */
  CancelReasonableQuestion: '/ReasonableQuestion/CancelReasonableQuestion',
  /** 删除合理性提问信息 */
  DeleteReasonableQuestion: '/ReasonableQuestion/DeleteReasonableQuestion',
  /** 沟通记录统计 */
  GetCommunicationRecordCount: '/ReasonableQuestion/GetCommunicationRecordCount',
  /** 获取合理性提问信息 */
  GetReasonableQuestionList: '/ReasonableQuestion/GetReasonableQuestionList',
  /** 修改合理性提问信息 */
  UpdateReasonableQuestion: '/ReasonableQuestion/UpdateReasonableQuestion',
}
export const apiAutoReconciliationRecord = {
  /** 新增对账记录信息 */
  AddReconciliationRecord: '/ReconciliationRecord/AddReconciliationRecord',
  /** 作废对账记录信息 */
  CancelReconciliationRecord: '/ReconciliationRecord/CancelReconciliationRecord',
  /** 删除对账记录信息 */
  DeleteReconciliationRecord: '/ReconciliationRecord/DeleteReconciliationRecord',
  /** 获取对账记录信息 */
  GetReconciliationRecordList: '/ReconciliationRecord/GetReconciliationRecordList',
  /** 修改对账记录信息 */
  UpdateReconciliationRecord: '/ReconciliationRecord/UpdateReconciliationRecord',
}
export const apiAutoReimburse = {
  /** 新增报销单 */
  AddReimburse: '/Reimburse/AddReimburse',
  /** 作废报销单 */
  CancelReimburse: '/Reimburse/CancelReimburse',
  /** 删除报销单 */
  DeleteReimburse: '/Reimburse/DeleteReimburse',
  /** 一般事物报销审批通过 */
  ExamineReimburse: '/Reimburse/ExamineReimburse',
  /** 获取报销单统计 */
  GetReimburseCount: '/Reimburse/GetReimburseCount',
  /** 导出 */
  GetReimburseDetailExcel: '/Reimburse/GetReimburseDetailExcel',
  /** 获取报销单列表 */
  GetReimburseList: '/Reimburse/GetReimburseList',
  /** 获取报销单列表汇总 */
  GetReimburseListSummary: '/Reimburse/GetReimburseListSummary',
  /** 获取单条报销单 */
  GetReimburseSingle: '/Reimburse/GetReimburseSingle',
  /** 一般事物报销核验 */
  HyReimburse: '/Reimburse/HyReimburse',
  /** 一般事物报销审批不通过 */
  UnExamineReimburse: '/Reimburse/UnExamineReimburse',
  /** 修改报销单 */
  UpdateReimburse: '/Reimburse/UpdateReimburse',
}
export const apiAutoReimburseSon = {
  /** 新增报销单子项 */
  AddReimburseSon: '/ReimburseSon/AddReimburseSon',
  /** 删除报销单子项 */
  DeleteReimburseSon: '/ReimburseSon/DeleteReimburseSon',
  /** 获取报销单子项列表 */
  GetReimburseSonList: '/ReimburseSon/GetReimburseSonList',
  /** 修改报销单子项 */
  UpdateReimburseSon: '/ReimburseSon/UpdateReimburseSon',
}
export const apiAutoReimburseType = {
  /** 新增报销类型 */
  AddReimburseType: '/ReimburseType/AddReimburseType',
  /** 删除报销类型 */
  DeleteReimburseType: '/ReimburseType/DeleteReimburseType',
  /** 获取报销类型列表 */
  GetReimburseTypeList: '/ReimburseType/GetReimburseTypeList',
  /** 修改报销类型 */
  UpdateReimburseType: '/ReimburseType/UpdateReimburseType',
}
export const apiAutoReport = {
  /** 造价咨询报表-金华 */
  GetProjectInfoReportJinHuaList: '/Report/GetProjectInfoReportJinHuaList',
  /** 造价咨询报表-金华-导出 */
  GetProjectInfoReportJinHuaListExcel: '/Report/GetProjectInfoReportJinHuaListExcel',
  /** 造价咨询报表-丽水 */
  GetProjectInfoReportLiShuiList: '/Report/GetProjectInfoReportLiShuiList',
  /** 造价咨询报表-丽水-导出 */
  GetProjectInfoReportLiShuiListExcel: '/Report/GetProjectInfoReportLiShuiListExcel',
  /** 造价咨询报表-杭州 */
  GetProjectInfoReportList: '/Report/GetProjectInfoReportList',
  /** 造价咨询报表-杭州-导出 */
  GetProjectInfoReportListExcel: '/Report/GetProjectInfoReportListExcel',
  /** 造价咨询报表-宁波 */
  GetProjectInfoReportNingBoList: '/Report/GetProjectInfoReportNingBoList',
  /** 造价咨询报表-宁波-导出 */
  GetProjectInfoReportNingBoListExcel: '/Report/GetProjectInfoReportNingBoListExcel',
  /** 造价咨询报表-温州 */
  GetProjectInfoReportWenZhouList: '/Report/GetProjectInfoReportWenZhouList',
  /** 造价咨询报表-温州-导出 */
  GetProjectInfoReportWenZhouListExcel: '/Report/GetProjectInfoReportWenZhouListExcel',
  /** 造价咨询报表-浙江省 */
  GetProjectInfoReportZheJiangList: '/Report/GetProjectInfoReportZheJiangList',
  /** 造价咨询报表-浙江省-导出 */
  GetProjectInfoReportZheJiangListExcel: '/Report/GetProjectInfoReportZheJiangListExcel',
}
export const apiAutoReportConfig = {
  /** 新增报表配置 */
  AddReportConfig: '/ReportConfig/AddReportConfig',
  /** 删除报表配置 */
  DeleteReportConfig: '/ReportConfig/DeleteReportConfig',
  /** 获取报表配置列表 */
  GetReportConfigList: '/ReportConfig/GetReportConfigList',
  /** 修改报表配置 */
  UpdateReportConfig: '/ReportConfig/UpdateReportConfig',
}
export const apiAutoReportRegionConfig = {
  /** 新增报表地区配置 */
  AddReportRegionConfig: '/ReportRegionConfig/AddReportRegionConfig',
  /** 删除报表地区配置 */
  DeleteReportRegionConfig: '/ReportRegionConfig/DeleteReportRegionConfig',
  /** 获取报表地区配置列表 */
  GetReportRegionConfigList: '/ReportRegionConfig/GetReportRegionConfigList',
  /** 修改报表地区配置 */
  UpdateReportRegionConfig: '/ReportRegionConfig/UpdateReportRegionConfig',
}
export const apiAutoReservedDocumentNo = {
  /** 新增造价咨询预留文号 */
  AddReservedDocumentNo: '/ReservedDocumentNo/AddReservedDocumentNo',
  /** 删除造价咨询预留文号 */
  DeleteReservedDocumentNo: '/ReservedDocumentNo/DeleteReservedDocumentNo',
  /** 造价咨询预留文号审批通过 */
  ExamineReservedDocumentNo: '/ReservedDocumentNo/ExamineReservedDocumentNo',
  /** 获取造价咨询预留文号Count */
  GetReservedDocumentNoCount: '/ReservedDocumentNo/GetReservedDocumentNoCount',
  /** 获取造价咨询预留文号列表 */
  GetReservedDocumentNoList: '/ReservedDocumentNo/GetReservedDocumentNoList',
  /** 获取造价咨询预留文号单条 */
  GetReservedDocumentNoSingle: '/ReservedDocumentNo/GetReservedDocumentNoSingle',
  /** 作废造价咨询预留文号 */
  IsCancelReservedDocumentNo: '/ReservedDocumentNo/IsCancelReservedDocumentNo',
  /** 造价咨询预留文号撤回 */
  ReservedDocumentNoReCall: '/ReservedDocumentNo/ReservedDocumentNoReCall',
  /** 造价咨询预留文号提交审批 */
  SubmitReservedDocumentNo: '/ReservedDocumentNo/SubmitReservedDocumentNo',
  /** 造价咨询预留文号审批不通过 */
  UnExamineReservedDocumentNo: '/ReservedDocumentNo/UnExamineReservedDocumentNo',
  /** 修改造价咨询预留文号 */
  UpdateReservedDocumentNo: '/ReservedDocumentNo/UpdateReservedDocumentNo',
}
export const apiAutoSeal = {
  /** 新增印章 */
  AddSeal: '/Seal/AddSeal',
  /** 删除印章 */
  DeleteSeal: '/Seal/DeleteSeal',
  /** 获取印章列表 */
  GetSealList: '/Seal/GetSealList',
  /** 修改印章 */
  UpdateSeal: '/Seal/UpdateSeal',
}
export const apiAutoSearchTemplate = {
  /** 新增搜索模板 */
  AddSearchTemplate: '/SearchTemplate/AddSearchTemplate',
  /** 删除搜索模板 */
  DeleteSearchTemplate: '/SearchTemplate/DeleteSearchTemplate',
  /** 获取搜索模板列表 */
  GetSearchTemplateList: '/SearchTemplate/GetSearchTemplateList',
  /** 修改搜索模板 */
  UpdateSearchTemplate: '/SearchTemplate/UpdateSearchTemplate',
}
export const apiAutoServiceType = {
  /** 新增服务类型 */
  AddServiceType: '/ServiceType/AddServiceType',
  /** 删除服务类型 */
  DeleteServiceType: '/ServiceType/DeleteServiceType',
  /** 获取服务类型列表 */
  GetServiceTypeList: '/ServiceType/GetServiceTypeList',
  /** 修改服务类型 */
  UpdateServiceType: '/ServiceType/UpdateServiceType',
}
export const apiAutoStMenuPower = {
  /** 新增菜单权限 */
  AddStMenuPower: '/StMenuPower/AddStMenuPower',
  /** 删除菜单权限 */
  DeleteStMenuPower: '/StMenuPower/DeleteStMenuPower',
  /** 获取菜单权限列表 */
  GetStMenuPowerList: '/StMenuPower/GetStMenuPowerList',
  /** 获取关联的指定权限记录 */
  GetStMenuPowerSingle: '/StMenuPower/GetStMenuPowerSingle',
  /** 修改菜单权限 */
  UpdateStMenuPower: '/StMenuPower/UpdateStMenuPower',
}
export const apiAutoSupervisionManualTemplate = {
  /** 新增监理手册 */
  AddSupervisionManualTemplate: '/SupervisionManualTemplate/AddSupervisionManualTemplate',
  /** 删除监理手册 */
  DeleteSupervisionManualTemplate: '/SupervisionManualTemplate/DeleteSupervisionManualTemplate',
  /** 获取监理手册列表 */
  GetSupervisionManualTemplateList: '/SupervisionManualTemplate/GetSupervisionManualTemplateList',
  /** 修改监理手册 */
  UpdateSupervisionManualTemplate: '/SupervisionManualTemplate/UpdateSupervisionManualTemplate',
}
export const apiAutoSuppleReport = {
  /** 新增补充报告 */
  AddSuppleReport: '/SuppleReport/AddSuppleReport',
  /** 作废补充报告 */
  CancelSuppleReport: '/SuppleReport/CancelSuppleReport',
  /** 删除补充报告 */
  DeleteSuppleReport: '/SuppleReport/DeleteSuppleReport',
  /** 补充报告审批通过 */
  ExamineSuppleReport: '/SuppleReport/ExamineSuppleReport',
  /** 补充报告统计 */
  GetSuppleReportCount: '/SuppleReport/GetSuppleReportCount',
  /** 获取补充报告列表 */
  GetSuppleReportList: '/SuppleReport/GetSuppleReportList',
  /** 获取单条补充报告 */
  GetSuppleReportSingle: '/SuppleReport/GetSuppleReportSingle',
  /** 补充报告归档接收 */
  SrArchiveJs: '/SuppleReport/SrArchiveJs',
  /** 补充报告归档提交 */
  SrArchiveSubmit: '/SuppleReport/SrArchiveSubmit',
  /** 补充报告归档--回退 */
  SrArchiveSubmitBack: '/SuppleReport/SrArchiveSubmitBack',
  /** 补充报告归档 */
  SrArchiveSubmitCd: '/SuppleReport/SrArchiveSubmitCd',
  /** 补充报告提交审批 */
  SuppleReportSubmit: '/SuppleReport/SuppleReportSubmit',
  /** 补充报告审批不通过 */
  UnExamineSuppleReport: '/SuppleReport/UnExamineSuppleReport',
  /** 修改补充报告 */
  UpdateSuppleReport: '/SuppleReport/UpdateSuppleReport',
  /** 修改补充报告 纸质接收 */
  UpdateSuppleReportZzjs: '/SuppleReport/UpdateSuppleReportZzjs',
}
export const apiAutoSuppleReportDelay = {
  /** 新增补充报告延期 */
  AddSuppleReportDelay: '/SuppleReportDelay/AddSuppleReportDelay',
  /** 删除补充报告延期 */
  DeleteSuppleReportDelay: '/SuppleReportDelay/DeleteSuppleReportDelay',
  /** 补充报告延期审批通过 */
  ExamineSuppleReportDelay: '/SuppleReportDelay/ExamineSuppleReportDelay',
  /** 补充报告延期统计 */
  GetSuppleReportDelayCount: '/SuppleReportDelay/GetSuppleReportDelayCount',
  /** 获取补充报告延期列表 */
  GetSuppleReportDelayList: '/SuppleReportDelay/GetSuppleReportDelayList',
  /** 单条补充报告延期 */
  GetSuppleReportDelaySingle: '/SuppleReportDelay/GetSuppleReportDelaySingle',
  /** 补充报告延期提交审批 */
  SuppleReportDelaySubmit: '/SuppleReportDelay/SuppleReportDelaySubmit',
  /** 补充报告延期审批不通过 */
  UnExamineSuppleReportDelay: '/SuppleReportDelay/UnExamineSuppleReportDelay',
  /** 修改补充报告延期 */
  UpdateSuppleReportDelay: '/SuppleReportDelay/UpdateSuppleReportDelay',
}
export const apiAutoSurveyRecord = {
  /** 新增踏勘记录信息 */
  AddSurveyRecord: '/SurveyRecord/AddSurveyRecord',
  /** 作废踏勘记录信息 */
  CancelSurveyRecord: '/SurveyRecord/CancelSurveyRecord',
  /** 删除踏勘记录信息 */
  DeleteSurveyRecord: '/SurveyRecord/DeleteSurveyRecord',
  /** 获取踏勘记录信息 */
  GetSurveyRecordList: '/SurveyRecord/GetSurveyRecordList',
  /** 修改踏勘记录信息 */
  UpdateSurveyRecord: '/SurveyRecord/UpdateSurveyRecord',
}
export const apiAutoSysGroup = {
  /** 新增人员部门组 */
  AddSysGroup: '/SysGroup/AddSysGroup',
  /** 删除人员部门组 */
  DeleteSysGroup: '/SysGroup/DeleteSysGroup',
  /** 获取人员部门组 列表 */
  GetSysGroupList: '/SysGroup/GetSysGroupList',
  /** 获取人员部门组  BY DoId */
  GetSysGroupSingle: '/SysGroup/GetSysGroupSingle',
  /** 修改人员部门组 */
  UpdateSysGroup: '/SysGroup/UpdateSysGroup',
}
export const apiAutoSystem = {
  /** 新增部门 */
  AddDdOrganization: '/System/AddDdOrganization',
  /** 新增花名册信息 */
  AddDdUserRoster: '/System/AddDdUserRoster',
  /** 新增人员 */
  AddSysAdminuser: '/System/AddSysAdminuser',
  /** 新增标签 */
  AddSysLabel: '/System/AddSysLabel',
  /** 新增菜单 */
  AddSysMenu: '/System/AddSysMenu',
  /** 添加地区配置 */
  AddSysRegion: '/System/AddSysRegion',
  /** 验证Token是否失效 */
  CheckLogin: '/System/CheckLogin',
  /** 删除部门 */
  DeleteDdOrganization: '/System/DeleteDdOrganization',
  /** 删除人员 */
  DeleteSysAdminuser: '/System/DeleteSysAdminuser',
  /** 删除标签 */
  DeleteSysLabel: '/System/DeleteSysLabel',
  /** 删除菜单 */
  DeleteSysMenu: '/System/DeleteSysMenu',
  /** 删除地区配置 */
  DeleteSysRegion: '/System/DeleteSysRegion',
  /** 人员导出 */
  ExportSysAdminuserListInfo: '/System/ExportSysAdminuserListInfo',
  /** 获取钉钉部门列表---统计 */
  GetDdOrganizationCount: '/System/GetDdOrganizationCount',
  /** 获取钉钉部门列表 */
  GetDdOrganizationList: '/System/GetDdOrganizationList',
  /** 获取单条花名册  by AuDdId */
  GetDdUserRosterSingle: '/System/GetDdUserRosterSingle',
  /** 获取登录日志列表 */
  GetLoginLogList: '/System/GetLoginLogList',
  /** 获取关联的指定权限记录 */
  GetPowerSingle: '/System/GetPowerSingle',
  /** 获取人员列表 */
  GetSysAdminuserList: '/System/GetSysAdminuserList',
  /** 获取人员列表--详情 */
  GetSysAdminuserListInfo: '/System/GetSysAdminuserListInfo',
  /** 获取标签列表 */
  GetSysLabelCount: '/System/GetSysLabelCount',
  /** 获取标签列表 */
  GetSysLabelList: '/System/GetSysLabelList',
  /** 获取菜单列表 */
  GetSysMenuList: '/System/GetSysMenuList',
  /** 获取地区配置 */
  GetSysRegionList: '/System/GetSysRegionList',
  /** 获取用户页面配置 */
  GetSysUserPageByKey: '/System/GetSysUserPageByKey',
  /** 是否是默认的密码 */
  IsDefaultPasswordAdminuser: '/System/IsDefaultPasswordAdminuser',
  /** 登录 */
  Login: '/System/Login',
  /** 验证码登录 */
  LoginBySms: '/System/LoginBySms',
  /** 更新用户页面配置 */
  SaveSysUserPage: '/System/SaveSysUserPage',
  /** 修改造价指标字段 */
  UpdateCostIndicatorsFieldJson: '/System/UpdateCostIndicatorsFieldJson',
  /** 修改部门 */
  UpdateDdOrganization: '/System/UpdateDdOrganization',
  /** 修改部门数据浏览权限 */
  UpdateDdOrganizationDataPower: '/System/UpdateDdOrganizationDataPower',
  /** 修改花名册信息 */
  UpdateDdUserRoster: '/System/UpdateDdUserRoster',
  /** 修改密码 */
  UpdatePassword: '/System/UpdatePassword',
  /** 修改密码--管理员 */
  UpdatePasswordInAdmin: '/System/UpdatePasswordInAdmin',
  /** 赋权限 */
  UpdatePowerInAdmin: '/System/UpdatePowerInAdmin',
  /** 修改人员 */
  UpdateSysAdminuser: '/System/UpdateSysAdminuser',
  /** 修改人员收藏 */
  UpdateSysAdminuserCollection: '/System/UpdateSysAdminuserCollection',
  /** 修改首页类型 */
  UpdateSysAdminuserIndexType: '/System/UpdateSysAdminuserIndexType',
  /** 修改人员业绩库json */
  UpdateSysAdminuserPerformanceBaseJson: '/System/UpdateSysAdminuserPerformanceBaseJson',
  /** 修改人员快捷功能 */
  UpdateSysAdminuserQuick: '/System/UpdateSysAdminuserQuick',
  /** 修改人员近期浏览 */
  UpdateSysAdminuserRecent: '/System/UpdateSysAdminuserRecent',
  /** 修改人员排序JSON */
  UpdateSysAdminuserSortJson: '/System/UpdateSysAdminuserSortJson',
  /** 系统禁用启用 */
  UpdateSysAdminuserState: '/System/UpdateSysAdminuserState',
  /** 修改个人用户数据浏览权限 */
  UpdateSysAdminuserlDataPower: '/System/UpdateSysAdminuserlDataPower',
  /** 修改标签 */
  UpdateSysLabel: '/System/UpdateSysLabel',
  /** 修改角色数据浏览权限 */
  UpdateSysLabelDataPower: '/System/UpdateSysLabelDataPower',
  /** 修改菜单 */
  UpdateSysMenu: '/System/UpdateSysMenu',
  /** 修改地区配置 */
  UpdateSysRegion: '/System/UpdateSysRegion',
  /** 部门同步 */
  synchronizationOrganization: '/System/synchronizationOrganization',
  /** 人员同步 */
  synchronizationUserInfo: '/System/synchronizationUserInfo',
  /** 人员花名册同步 */
  synchronizationUserRoster: '/System/synchronizationUserRoster',
}
export const apiAutoTemplateManage = {
  /** 批量新增模板 */
  AddTemplateManage: '/TemplateManage/AddTemplateManage',
  /** 新增模板 */
  AddTemplateManageNew: '/TemplateManage/AddTemplateManageNew',
  /** 删除模板 */
  DeleteTemplateManage: '/TemplateManage/DeleteTemplateManage',
  /** 获取模板列表 */
  GetTemplateManageList: '/TemplateManage/GetTemplateManageList',
  /** 批量修改模板 */
  UpdateTemplateManage: '/TemplateManage/UpdateTemplateManage',
  /** 修改模板 */
  UpdateTemplateManageNew: '/TemplateManage/UpdateTemplateManageNew',
}
export const apiAutoTemplateManageConfig = {
  /** 新增模板配置 */
  AddTemplateManageConfig: '/TemplateManageConfig/AddTemplateManageConfig',
  /** 删除模板配置 */
  DeleteTemplateManageConfig: '/TemplateManageConfig/DeleteTemplateManageConfig',
  /** 获取模板配置列表 */
  GetTemplateManageConfigList: '/TemplateManageConfig/GetTemplateManageConfigList',
  /** 修改模板配置 */
  UpdateTemplateManageConfig: '/TemplateManageConfig/UpdateTemplateManageConfig',
}
export const apiAutoTradeType = {
  /** 新增行业类型 */
  AddTradeType: '/TradeType/AddTradeType',
  /** 删除行业类型 */
  DeleteTradeType: '/TradeType/DeleteTradeType',
  /** 获取行业类型列表 */
  GetTradeTypeList: '/TradeType/GetTradeTypeList',
  /** 修改行业类型 */
  UpdateTradeType: '/TradeType/UpdateTradeType',
}
export const apiAutoTravelReimbursement = {
  /** 新增差旅报销 */
  AddTravelReimbursement: '/TravelReimbursement/AddTravelReimbursement',
  /** 作废差旅报销 */
  CancelTravelReimbursement: '/TravelReimbursement/CancelTravelReimbursement',
  /** 删除差旅报销 */
  DeleteTravelReimbursement: '/TravelReimbursement/DeleteTravelReimbursement',
  /** 差旅报销审批通过 */
  ExamineTravelReimbursement: '/TravelReimbursement/ExamineTravelReimbursement',
  /** 差旅报销审批通过--补贴 */
  ExamineTravelReimbursementBt: '/TravelReimbursement/ExamineTravelReimbursementBt',
  /** 获取差旅报销列表--住宿列表 */
  GetTravelAccommodationList: '/TravelReimbursement/GetTravelAccommodationList',
  /** 获取差旅报销列表--其他列表 */
  GetTravelOtherList: '/TravelReimbursement/GetTravelOtherList',
  /** 获取差旅报销列表--人员列表 */
  GetTravelPerList: '/TravelReimbursement/GetTravelPerList',
  /** 获取差旅报销列表 */
  GetTravelReimbursementList: '/TravelReimbursement/GetTravelReimbursementList',
  /** 获取差旅报销列表--交通列表 */
  GetTravelTrafficList: '/TravelReimbursement/GetTravelTrafficList',
  /** 差旅报销审批不通过 */
  UnExamineTravelReimbursement: '/TravelReimbursement/UnExamineTravelReimbursement',
  /** 差旅报销审批不通过--补贴 */
  UnExamineTravelReimbursementBt: '/TravelReimbursement/UnExamineTravelReimbursementBt',
  /** 修改差旅报销 */
  UpdateTravelReimbursement: '/TravelReimbursement/UpdateTravelReimbursement',
}
export const apiAutoTycApi = {
  /** 企查查--查询企业名称列表 */
  GetCompanyNameListByTyc: '/TycApi/GetCompanyNameListByTyc',
  /** 天眼查--企业工商信息详情 */
  synchronizationBasicDetailsByName: '/TycApi/synchronizationBasicDetailsByName',
}
export const apiAutoTykjTemplate = {
  /** 新增统一口径模板 */
  AddTykjTemplate: '/TykjTemplate/AddTykjTemplate',
  /** 删除统一口径模板 */
  DeleteTykjTemplate: '/TykjTemplate/DeleteTykjTemplate',
  /** 获取统一口径模板列表 */
  GetTykjTemplateList: '/TykjTemplate/GetTykjTemplateList',
  /** 修改统一口径模板 */
  UpdateTykjTemplate: '/TykjTemplate/UpdateTykjTemplate',
}
export const apiAutoUpdateLog = {
  /** 新增更新日志 */
  AddUpdateLog: '/UpdateLog/AddUpdateLog',
  /** 删除更新日志 */
  DeleteUpdateLog: '/UpdateLog/DeleteUpdateLog',
  /** 获取更新日志列表 */
  GetUpdateLogList: '/UpdateLog/GetUpdateLogList',
  /** 修改更新日志 */
  UpdateUpdateLog: '/UpdateLog/UpdateUpdateLog',
}
export const apiAutoUploadDocument = {
  /** 新增全局上传文档 */
  AddUploadDocument: '/UploadDocument/AddUploadDocument',
  /** 删除全局上传文档 */
  DeleteUploadDocument: '/UploadDocument/DeleteUploadDocument',
  /** 获取全局上传文档列表 */
  GetUploadDocumentList: '/UploadDocument/GetUploadDocumentList',
  /** 修改全局上传文档 */
  UpdateUploadDocument: '/UploadDocument/UpdateUploadDocument',
  /** 修改排序 */
  UpdateUploadDocumentSort: '/UploadDocument/UpdateUploadDocumentSort',
}
export const apiAutoUploadPayRoll = {
  /** 上传工资单 */
  UploadPayroll: '/UploadPayRoll/UploadPayroll',
}
export const apiAutoUserCertificateServic = {
  /** 新增员工证书 */
  AddUserCertificate: '/UserCertificateServic/AddUserCertificate',
  /** 删除员工证书 */
  DeleteUserCertificate: '/UserCertificateServic/DeleteUserCertificate',
  /** 导出员工资质 */
  ExportUserCertificate: '/UserCertificateServic/ExportUserCertificate',
  /** 获取员工证书列表 */
  GetUserCertificateList: '/UserCertificateServic/GetUserCertificateList',
  /** 修改员工证书 */
  UpdateUserCertificate: '/UserCertificateServic/UpdateUserCertificate',
}
export const apiAutoUserPro = {
  /** 获取用户项目浏览列表 */
  GetUserProList: '/UserPro/GetUserProList',
  /** 修改用户项目浏览 */
  UpdateUserPro: '/UserPro/UpdateUserPro',
}
export const apiAutoVehicle = {
  /** 新增车辆 */
  AddVehicle: '/Vehicle/AddVehicle',
  /** 删除车辆 */
  DeleteVehicle: '/Vehicle/DeleteVehicle',
  /** 获取车辆列表 */
  GetVehicleList: '/Vehicle/GetVehicleList',
  /** 获取车辆单条 */
  GetVehicleSingle: '/Vehicle/GetVehicleSingle',
  /** 修改车辆 */
  UpdateVehicle: '/Vehicle/UpdateVehicle',
}
export const apiAutoVehicleApply = {
  /** 新增用车申请 */
  AddVehicleApply: '/VehicleApply/AddVehicleApply',
  /** 取消预约 */
  CancelVehicleApplyYy: '/VehicleApply/CancelVehicleApplyYy',
  /** 删除用车申请 */
  DeleteVehicleApply: '/VehicleApply/DeleteVehicleApply',
  /** 用车申请审批通过--预约 */
  DistributionVehicleApply: '/VehicleApply/DistributionVehicleApply',
  /** 用车申请审批通过 */
  ExamineVehicleApply: '/VehicleApply/ExamineVehicleApply',
  /** 获取用车申请Count */
  GetVehicleApplyCount: '/VehicleApply/GetVehicleApplyCount',
  /** 获取用车申请列表 */
  GetVehicleApplyList: '/VehicleApply/GetVehicleApplyList',
  /** 获取用车申请列表使用情况 */
  GetVehicleApplyListYy: '/VehicleApply/GetVehicleApplyListYy',
  /** 获取用车申请单条 */
  GetVehicleApplySingle: '/VehicleApply/GetVehicleApplySingle',
  /** 用车申请提交审批 */
  SubmitVehicleApply: '/VehicleApply/SubmitVehicleApply',
  /** 用车申请审批不通过 */
  UnExamineVehicleApply: '/VehicleApply/UnExamineVehicleApply',
  /** 修改用车申请 */
  UpdateVehicleApply: '/VehicleApply/UpdateVehicleApply',
  /** 用车申请撤回 */
  VehicleApplyReCall: '/VehicleApply/VehicleApplyReCall',
}
export const apiAutoVerifyCode = {
  /**  */
  Generate: '/VerifyCode/Generate',
  /**  */
  SendSms: '/VerifyCode/SendSms',
}
export const apiAutoVideo = {
  /** 新增视频 */
  AddVideo: '/Video/AddVideo',
  /** 新增视频浏览 */
  AddVideoBrowse: '/Video/AddVideoBrowse',
  /** 新增视频评价 */
  AddVideoEvaluate: '/Video/AddVideoEvaluate',
  /** 点击量累加 */
  AddVideoHits: '/Video/AddVideoHits',
  /** 删除视频 */
  DeleteVideo: '/Video/DeleteVideo',
  /** 删除视频评价 */
  DeleteVideoEvaluate: '/Video/DeleteVideoEvaluate',
  /** 获取视频浏览列表 */
  GetVideoBrowseList: '/Video/GetVideoBrowseList',
  /** 获取视频评价列表 */
  GetVideoEvaluateList: '/Video/GetVideoEvaluateList',
  /** 获取视频列表 */
  GetVideoList: '/Video/GetVideoList',
  /** 获取单个视频 */
  GetVideoSingle: '/Video/GetVideoSingle',
  /** 修改视频 */
  UpdateVideo: '/Video/UpdateVideo',
  /** 修改视频评价 */
  UpdateVideoEvaluate: '/Video/UpdateVideoEvaluate',
}
export const apiAutoVideoType = {
  /** 新增视频类型 */
  AddVideoType: '/VideoType/AddVideoType',
  /** 删除视频类型 */
  DeleteVideoType: '/VideoType/DeleteVideoType',
  /** 获取视频类型列表 */
  GetVideoTypeList: '/VideoType/GetVideoTypeList',
  /** 修改视频类型 */
  UpdateVideoType: '/VideoType/UpdateVideoType',
}
export const apiAutoWelfare = {
  /** 新增员工福利 */
  AddWelfare: '/Welfare/AddWelfare',
  /** 删除员工福利 */
  DeleteWelfare: '/Welfare/DeleteWelfare',
  /** 获取员工福利列表 */
  GetWelfareList: '/Welfare/GetWelfareList',
  /** 员工福利作废 */
  IsCancelWelfare: '/Welfare/IsCancelWelfare',
  /** 修改员工福利 */
  UpdateWelfare: '/Welfare/UpdateWelfare',
}
export const apiAutoWelfareType = {
  /** 新增员工福利类型 */
  AddWelfareType: '/WelfareType/AddWelfareType',
  /** 删除员工福利类型 */
  DeleteWelfareType: '/WelfareType/DeleteWelfareType',
  /** 获取员工福利类型列表 */
  GetWelfareTypeList: '/WelfareType/GetWelfareTypeList',
  /** 修改员工福利类型 */
  UpdateWelfareType: '/WelfareType/UpdateWelfareType',
}
export const apiAutoWordTemplate = {
  /** 添加全过程咨询word模板文件 */
  AddWordTemplateQgczx: '/WordTemplate/AddWordTemplateQgczx',
  /** 添加造价咨询word模板文件 */
  AddWordTemplateZjzx: '/WordTemplate/AddWordTemplateZjzx',
  /** 删除全过程咨询word模板文件 */
  DeleteWordTemplatQgczx: '/WordTemplate/DeleteWordTemplatQgczx',
  /** 删除造价咨询word模板文件 */
  DeleteWordTemplateZjzx: '/WordTemplate/DeleteWordTemplateZjzx',
  /** 获取全过程咨询word模板文件 */
  GetWordTemplateQgczxList: '/WordTemplate/GetWordTemplateQgczxList',
  /** 获取造价咨询word模板文件 */
  GetWordTemplateZjzxList: '/WordTemplate/GetWordTemplateZjzxList',
  /** 渲染全过程咨询文档 */
  RenderingWordTemplateQgczx: '/WordTemplate/RenderingWordTemplateQgczx',
  /** 渲染造价咨询文档 */
  RenderingWordTemplateZjzx: '/WordTemplate/RenderingWordTemplateZjzx',
  /** 更新全过程咨询word模板文件 */
  UpdateWordTemplateQgczx: '/WordTemplate/UpdateWordTemplateQgczx',
  /** 更新造价咨询word模板文件 */
  UpdateWordTemplateZjzx: '/WordTemplate/UpdateWordTemplateZjzx',
}
export const apiAutoWorkContactLetter = {
  /** 新增工作联系函 */
  AddWorkContactLetter: '/WorkContactLetter/AddWorkContactLetter',
  /** 作废工作联系函单条 */
  CancelWorkContactLetter: '/WorkContactLetter/CancelWorkContactLetter',
  /** 删除工作联系函 */
  DeleteWorkContactLetter: '/WorkContactLetter/DeleteWorkContactLetter',
  /** 工作联系函审批通过 */
  ExamineWorkContactLetter: '/WorkContactLetter/ExamineWorkContactLetter',
  /** 获取工作联系函Count */
  GetWorkContactLetterCount: '/WorkContactLetter/GetWorkContactLetterCount',
  /** 获取工作联系函列表 */
  GetWorkContactLetterList: '/WorkContactLetter/GetWorkContactLetterList',
  /** 获取工作联系函单条 */
  GetWorkContactLetterSingle: '/WorkContactLetter/GetWorkContactLetterSingle',
  /** 获取工作函单条数据--by  工作函盖章id */
  GetWorkContactLetterSingleByPusId: '/WorkContactLetter/GetWorkContactLetterSingleByPusId',
  /** 工作联系函审批不通过 */
  UnExamineWorkContactLetter: '/WorkContactLetter/UnExamineWorkContactLetter',
  /** 修改工作联系函 */
  UpdateWorkContactLetter: '/WorkContactLetter/UpdateWorkContactLetter',
  /** 修改工作联系函编号 */
  UpdateWorkContactLetterNo: '/WorkContactLetter/UpdateWorkContactLetterNo',
  /** 工作联系函提交审批 */
  WorkContactLetterSubmit: '/WorkContactLetter/WorkContactLetterSubmit',
}
export const apiAutoXmlBasis = {
  /** 新增依据配置 */
  AddXmlBasis: '/XmlBasis/AddXmlBasis',
  /** 删除依据配置 */
  DeleteXmlBasis: '/XmlBasis/DeleteXmlBasis',
  /** 获取依据配置列表 */
  GetXmlBasisList: '/XmlBasis/GetXmlBasisList',
  /** 修改依据配置 */
  UpdateXmlBasis: '/XmlBasis/UpdateXmlBasis',
}
export const apiAutoXmlCl2010Config = {
  /** 新增2010材料配置 */
  AddXmlCl2010Config: '/XmlCl2010Config/AddXmlCl2010Config',
  /** 删除2010材料配置 */
  DeleteXmlCl2010Config: '/XmlCl2010Config/DeleteXmlCl2010Config',
  /** 获取2010材料配置列表 */
  GetXmlCl2010ConfigList: '/XmlCl2010Config/GetXmlCl2010ConfigList',
  /** 获取2010材料配置列表--表 */
  GetXmlCl2010ConfigTList: '/XmlCl2010Config/GetXmlCl2010ConfigTList',
  /** 修改2010材料配置 */
  UpdateXmlCl2010Config: '/XmlCl2010Config/UpdateXmlCl2010Config',
}
export const apiAutoXmlCl2018Config = {
  /** 新增2018材料配置 */
  AddXmlCl2018Config: '/XmlCl2018Config/AddXmlCl2018Config',
  /** 删除2018材料配置 */
  DeleteXmlCl2018Config: '/XmlCl2018Config/DeleteXmlCl2018Config',
  /** 获取2018材料配置列表 */
  GetXmlCl2018ConfigList: '/XmlCl2018Config/GetXmlCl2018ConfigList',
  /** 获取2018材料配置列表-表 */
  GetXmlCl2018ConfigTList: '/XmlCl2018Config/GetXmlCl2018ConfigTList',
  /** 修改2018材料配置 */
  UpdateXmlCl2018Config: '/XmlCl2018Config/UpdateXmlCl2018Config',
}
export const apiAutoXmlClGroup = {
  /** 新增材料组配置 */
  AddXmlClGroup: '/XmlClGroup/AddXmlClGroup',
  /** 删除材料组配置 */
  DeleteXmlClGroup: '/XmlClGroup/DeleteXmlClGroup',
  /** 获取材料组配置列表 */
  GetXmlClGroupList: '/XmlClGroup/GetXmlClGroupList',
  /** 修改材料组配置 */
  UpdateXmlClGroup: '/XmlClGroup/UpdateXmlClGroup',
}
export const apiAutoXmlComprehensive = {
  /** 新增组织措施 */
  AddGroupListNew: '/XmlComprehensive/AddGroupListNew',
  /** 新增组织措施 */
  AddXmlGroupList: '/XmlComprehensive/AddXmlGroupList',
  /** 批量修改面积 */
  BathUpdateZhEconomic: '/XmlComprehensive/BathUpdateZhEconomic',
  /** 生成综合经济指标 */
  CreateEconomic: '/XmlComprehensive/CreateEconomic',
  /** 生成透视树 */
  CreateTsTree: '/XmlComprehensive/CreateTsTree',
  /** 生成专业经济指标 */
  CreateZyEconomic: '/XmlComprehensive/CreateZyEconomic',
  /** 删除组织措施 */
  DelGroupListNew: '/XmlComprehensive/DelGroupListNew',
  /** 删除组织措施 */
  DeleteXmlGroupList: '/XmlComprehensive/DeleteXmlGroupList',
  /** 查看清单对应的定额--分部分项 */
  GetDeListInAdmin: '/XmlComprehensive/GetDeListInAdmin',
  /** 查询分部分项清单 */
  GetGclReListInAdmin: '/XmlComprehensive/GetGclReListInAdmin',
  /** 获获取工程费总汇 */
  GetGcxmPriceListInAdminNew: '/XmlComprehensive/GetGcxmPriceListInAdminNew',
  /** 获取组织措施 */
  GetGroupListInAdminNew: '/XmlComprehensive/GetGroupListInAdminNew',
  /** 查询分部分项清单2018 */
  GetRcjHjTjNewList2018Unit: '/XmlComprehensive/GetRcjHjTjNewList2018Unit',
  /** 人才机统计2010 */
  GetRcjTjListInAdminNew2010: '/XmlComprehensive/GetRcjTjListInAdminNew2010',
  /** 人才机统计--单位工程2010 */
  GetRcjTjListInAdminNew2010Unit: '/XmlComprehensive/GetRcjTjListInAdminNew2010Unit',
  /** 人才机统计2018 */
  GetRcjTjListInAdminNew2018: '/XmlComprehensive/GetRcjTjListInAdminNew2018',
  /** 分部分项 */
  GetSubitemModel: '/XmlComprehensive/GetSubitemModel',
  /** 分部分项 */
  GetSubitemModelInAdminNew: '/XmlComprehensive/GetSubitemModelInAdminNew',
  /** 技术措施 */
  GetTechModel: '/XmlComprehensive/GetTechModel',
  /** 技术措施 */
  GetTechModelInAdminNew: '/XmlComprehensive/GetTechModelInAdminNew',
  /** 查看清单对应的定额--技术措施 */
  GetTgDeListInAdmin: '/XmlComprehensive/GetTgDeListInAdmin',
  /** 树结构 */
  GetTreeModel: '/XmlComprehensive/GetTreeModel',
  /** 树结构--分部分项 */
  GetTreeModelSt: '/XmlComprehensive/GetTreeModelSt',
  /** 树结构--技术措施 */
  GetTreeModelTg: '/XmlComprehensive/GetTreeModelTg',
  /** 查询透视树 */
  GetTsTreeListInAdmin: '/XmlComprehensive/GetTsTreeListInAdmin',
  /** 单位人材机总汇 */
  GetUnitRcjListInAdminNew: '/XmlComprehensive/GetUnitRcjListInAdminNew',
  /** 单位人材机总汇--统计数据 */
  GetUnitRcjTjListInAdminNew: '/XmlComprehensive/GetUnitRcjTjListInAdminNew',
  /** 单位人材机总汇--统计数据 */
  GetUnitRcjTjListInAdminNewUnit: '/XmlComprehensive/GetUnitRcjTjListInAdminNewUnit',
  /** 单位工程总汇 */
  GetUnitZhModel: '/XmlComprehensive/GetUnitZhModel',
  /** 建设项目 */
  GetXmlBuildProList: '/XmlComprehensive/GetXmlBuildProList',
  /** 工程费总汇 */
  GetXmlGcxmPriceList: '/XmlComprehensive/GetXmlGcxmPriceList',
  /** 获取组织措施列表 */
  GetXmlGroupListList: '/XmlComprehensive/GetXmlGroupListList',
  /** 人材机汇总 */
  GetXmlRcjRecordList: '/XmlComprehensive/GetXmlRcjRecordList',
  /** 建设项目招标控制 */
  GetXmlTenderList: '/XmlComprehensive/GetXmlTenderList',
  /** 单位工程 */
  GetXmlUnitProList: '/XmlComprehensive/GetXmlUnitProList',
  /** 单位人材机总汇 */
  GetXmlUnitRcjList: '/XmlComprehensive/GetXmlUnitRcjList',
  /** 整体工程 */
  GetXmlWholeEngineerList: '/XmlComprehensive/GetXmlWholeEngineerList',
  /** 综合经济指标 */
  GetZhEconomicListInAdmin: '/XmlComprehensive/GetZhEconomicListInAdmin',
  /** 专业经济指标 */
  GetZyEconomicListInAdmin: '/XmlComprehensive/GetZyEconomicListInAdmin',
  /** 修改计组织措施 */
  UpdateGroupListNew: '/XmlComprehensive/UpdateGroupListNew',
  /** 修改组织措施 */
  UpdateXmlGroupList: '/XmlComprehensive/UpdateXmlGroupList',
  /** 修改综合经济指标 */
  UpdateZhEconomic: '/XmlComprehensive/UpdateZhEconomic',
  /** 修改专业经济指标 */
  UpdateZyEconomic: '/XmlComprehensive/UpdateZyEconomic',
}
export const apiAutoXmlEngineer = {
  /** 新增Xml工程 */
  AddXmlEngineer: '/XmlEngineer/AddXmlEngineer',
  /** 删除Xml工程 */
  DeleteXmlEngineer: '/XmlEngineer/DeleteXmlEngineer',
  /**  */
  GetAzfConfigInAdmin: '/XmlEngineer/GetAzfConfigInAdmin',
  /** 获取Xml工程列表 */
  GetXmlEngineerList: '/XmlEngineer/GetXmlEngineerList',
  /** 获取Xml工程单条 */
  GetXmlEngineerSingle: '/XmlEngineer/GetXmlEngineerSingle',
  /** 修改Xml工程 */
  UpdateXmlEngineer: '/XmlEngineer/UpdateXmlEngineer',
}
export const apiAutoXmlFile = {
  /** 新增XML文件 */
  AddXmlFile: '/XmlFile/AddXmlFile',
  /** 获取XML文件列表 */
  GetXmlFileList: '/XmlFile/GetXmlFileList',
  /** 修改XML文件 */
  UpdateXmlfile: '/XmlFile/UpdateXmlfile',
}
export const apiAutoXmlImgfile = {
  /** 新增Xml图纸 */
  AddXmlImgfile: '/XmlImgfile/AddXmlImgfile',
  /** 删除Xml图纸 */
  DeleteXmlImgfile: '/XmlImgfile/DeleteXmlImgfile',
  /** 获取Xml图纸列表 */
  GetXmlImgfileList: '/XmlImgfile/GetXmlImgfileList',
  /** 修改Xml图纸 */
  UpdateXmlImgfile: '/XmlImgfile/UpdateXmlImgfile',
}
export const apiAutoXmlImgfileType = {
  /** 新增Xml图纸类型 */
  AddXmlImgfileType: '/XmlImgfileType/AddXmlImgfileType',
  /** 删除Xml图纸类型 */
  DeleteXmlImgfileType: '/XmlImgfileType/DeleteXmlImgfileType',
  /** 获取Xml图纸类型列表 */
  GetXmlImgfileTypeList: '/XmlImgfileType/GetXmlImgfileTypeList',
  /** 修改Xml图纸类型 */
  UpdateXmlImgfileType: '/XmlImgfileType/UpdateXmlImgfileType',
}
export const apiAutoXmlMajor = {
  /** 新增专业配置 */
  AddXmlMajor: '/XmlMajor/AddXmlMajor',
  /** 删除专业配置 */
  DeleteXmlMajor: '/XmlMajor/DeleteXmlMajor',
  /** 获取专业配置列表 */
  GetXmlMajorList: '/XmlMajor/GetXmlMajorList',
  /** 修改专业配置 */
  UpdateXmlMajor: '/XmlMajor/UpdateXmlMajor',
}
export const apiAutoXmlProfile = {
  /** 新增Xml项目文档 */
  AddXmlProfile: '/XmlProfile/AddXmlProfile',
  /** 删除Xml项目文档 */
  DeleteXmlProfile: '/XmlProfile/DeleteXmlProfile',
  /** 获取Xml项目文档列表 */
  GetXmlProfileList: '/XmlProfile/GetXmlProfileList',
  /** 修改Xml项目文档 */
  UpdateXmlProfile: '/XmlProfile/UpdateXmlProfile',
}
export const apiAutoXmlProfileType = {
  /** 新增Xml项目文档类型 */
  AddXmlProfileType: '/XmlProfileType/AddXmlProfileType',
  /** 删除Xml项目文档类型 */
  DeleteXmlProfileType: '/XmlProfileType/DeleteXmlProfileType',
  /** 获取Xml项目文档类型列表 */
  GetXmlProfileTypeList: '/XmlProfileType/GetXmlProfileTypeList',
  /** 修改Xml项目文档类型 */
  UpdateXmlProfileType: '/XmlProfileType/UpdateXmlProfileType',
}
export const apiAutoXmlStandardPro = {
  /** 添加关联-分部分项 */
  AddStandardReBathSt: '/XmlStandardPro/AddStandardReBathSt',
  /** 添加关联-技术措施 */
  AddStandardReBathTg: '/XmlStandardPro/AddStandardReBathTg',
  /** 添加关联-暂存 */
  AddStandardReBathZc: '/XmlStandardPro/AddStandardReBathZc',
  /** 新增标准类型 */
  AddXmlStandardPro: '/XmlStandardPro/AddXmlStandardPro',
  /** 新增标准类型--批量 */
  AddXmlStandardProBath: '/XmlStandardPro/AddXmlStandardProBath',
  /** 添加关联--专业工程 */
  AddXmlStandardReBath: '/XmlStandardPro/AddXmlStandardReBath',
  /** 新增技术措施定额配置表 */
  AddXmlTgzhdjZh: '/XmlStandardPro/AddXmlTgzhdjZh',
  /** 新增分部分项定额配置表 */
  AddXmlZhdjZh: '/XmlStandardPro/AddXmlZhdjZh',
  /** 生成分部分项清单和定额 */
  CreateFbFxQdListDe: '/XmlStandardPro/CreateFbFxQdListDe',
  /** 生成技术措施清单和定额 */
  CreateTgQdListDe: '/XmlStandardPro/CreateTgQdListDe',
  /** 删除分部分项清单定额总汇表 */
  DelGclZh: '/XmlStandardPro/DelGclZh',
  /** 删除分部分项清单定额总汇表 */
  DelTgclZh: '/XmlStandardPro/DelTgclZh',
  /** 删除标准类型 */
  DeleteXmlStandardPro: '/XmlStandardPro/DeleteXmlStandardPro',
  /** 删除技术措施定额配置表 */
  DeleteXmlTgzhdjZh: '/XmlStandardPro/DeleteXmlTgzhdjZh',
  /** 修删除分部分项定额配置表 */
  DeleteXmlZhdjZh: '/XmlStandardPro/DeleteXmlZhdjZh',
  /** 清空项目模板,项目关联，新组织措施，新项目总汇 */
  EmptyStandardProBath: '/XmlStandardPro/EmptyStandardProBath',
  /** 分部分项清单定额总汇表---带定额 */
  GetGclZhListDeInAdmin: '/XmlStandardPro/GetGclZhListDeInAdmin',
  /** 分部分项清单定额总汇表 */
  GetGclZhListInAdmin: '/XmlStandardPro/GetGclZhListInAdmin',
  /** 技术措施清单定额总汇表---带定额 */
  GetTgclZhListDeInAdmin: '/XmlStandardPro/GetTgclZhListDeInAdmin',
  /** 技术措施清单定额总汇表 */
  GetTgclZhListInAdmin: '/XmlStandardPro/GetTgclZhListInAdmin',
  /** 获取标准类型列表 */
  GetXmlStandardProList: '/XmlStandardPro/GetXmlStandardProList',
  /** 获取标准类型列表--新 */
  GetXmlStandardProListNew: '/XmlStandardPro/GetXmlStandardProListNew',
  /** 获取关联 */
  GetXmlStandardRelationList: '/XmlStandardPro/GetXmlStandardRelationList',
  /** 获取技术措施定额配置表 */
  GetXmlTgzhdjZhList: '/XmlStandardPro/GetXmlTgzhdjZhList',
  /** 获取分部分项定额配置表 */
  GetXmlZhdjZhList: '/XmlStandardPro/GetXmlZhdjZhList',
  /** 分部分项清单定额总汇表设为优秀案例 */
  SetGclZhFine: '/XmlStandardPro/SetGclZhFine',
  /** 技术措施清单定额总汇表设为优秀案例 */
  SetTgclZhFine: '/XmlStandardPro/SetTgclZhFine',
  /** 修改分部分项清单定额总汇表 */
  UpdateGclZh: '/XmlStandardPro/UpdateGclZh',
  /** 修改技术措施清单定额总汇表 */
  UpdateTgclZh: '/XmlStandardPro/UpdateTgclZh',
  /** 修改标准类型 */
  UpdateXmlStandardPro: '/XmlStandardPro/UpdateXmlStandardPro',
  /** 修改技术措施定额配置表 */
  UpdateXmlTgzhdjZh: '/XmlStandardPro/UpdateXmlTgzhdjZh',
  /** 修改分部分项定额配置表 */
  UpdateXmlZhdjZh: '/XmlStandardPro/UpdateXmlZhdjZh',
}
export const apiAutoXmlStandardType = {
  /** 新增类型配置 */
  AddXmlStandardType: '/XmlStandardType/AddXmlStandardType',
  /** 删除类型配置 */
  DeleteXmlStandardType: '/XmlStandardType/DeleteXmlStandardType',
  /** 获取类型配置列表 */
  GetXmlStandardTypeList: '/XmlStandardType/GetXmlStandardTypeList',
  /** 修改类型配置 */
  UpdateXmlStandardType: '/XmlStandardType/UpdateXmlStandardType',
}
export const apiAutoXmlSubitemConfig = {
  /** 新增分部分项配置 */
  AddXmlSubitemConfig: '/XmlSubitemConfig/AddXmlSubitemConfig',
  /** 删除分部分项配置 */
  DeleteXmlSubitemConfig: '/XmlSubitemConfig/DeleteXmlSubitemConfig',
  /** 获取分部分项配置列表 */
  GetXmlSubitemConfigList: '/XmlSubitemConfig/GetXmlSubitemConfigList',
  /** 修改分部分项配置 */
  UpdateXmlSubitemConfig: '/XmlSubitemConfig/UpdateXmlSubitemConfig',
}
export const apiAutoXmlSyTarget = {
  /** 新增综合指标配置 */
  AddXmlSyTarget: '/XmlSyTarget/AddXmlSyTarget',
  /** 删除综合指标配置 */
  DeleteXmlSyTarget: '/XmlSyTarget/DeleteXmlSyTarget',
  /** 获取综合指标配置列表 */
  GetXmlSyTargetList: '/XmlSyTarget/GetXmlSyTargetList',
  /** 修改综合指标配置 */
  UpdateXmlSyTarget: '/XmlSyTarget/UpdateXmlSyTarget',
}
export const apiAutoXmlTgConfig = {
  /** 新增技术措施 */
  AddXmlTgConfig: '/XmlTgConfig/AddXmlTgConfig',
  /** 删除技术措施 */
  DeleteXmlTgConfig: '/XmlTgConfig/DeleteXmlTgConfig',
  /** 获取技术措施列表 */
  GetXmlTgConfigList: '/XmlTgConfig/GetXmlTgConfigList',
  /** 修改技术措施 */
  UpdateXmlTgConfig: '/XmlTgConfig/UpdateXmlTgConfig',
}
export const apiAutoXmlTyConfig = {
  /** 新增通用配置 */
  AddXmlTyConfig: '/XmlTyConfig/AddXmlTyConfig',
  /** 删除通用配置 */
  DeleteXmlTyConfig: '/XmlTyConfig/DeleteXmlTyConfig',
  /** 获取通用配置列表 */
  GetXmlTyConfigList: '/XmlTyConfig/GetXmlTyConfigList',
  /** 修改通用配置 */
  UpdateXmlTyConfig: '/XmlTyConfig/UpdateXmlTyConfig',
}
export const apiAutoXxjAddress = {
  /** 新增一级地址 */
  AddXxjAddress: '/XxjAddress/AddXxjAddress',
  /** 删除一级地址 */
  DeleteXxjAddress: '/XxjAddress/DeleteXxjAddress',
  /** 获取一级地址列表 */
  GetXxjAddressList: '/XxjAddress/GetXxjAddressList',
  /** 获取一级地址单条 */
  GetXxjAddressSingle: '/XxjAddress/GetXxjAddressSingle',
  /** 修改一级地址 */
  UpdateXxjAddress: '/XxjAddress/UpdateXxjAddress',
}
export const apiAutoXxjAddressSon = {
  /** 新增二级地址 */
  AddXxjAddressSon: '/XxjAddressSon/AddXxjAddressSon',
  /** 删除二级地址 */
  DeleteXxjAddressSon: '/XxjAddressSon/DeleteXxjAddressSon',
  /** 获取二级地址列表 */
  GetXxjAddressSonList: '/XxjAddressSon/GetXxjAddressSonList',
  /** 获取二级地址单条 */
  GetXxjAddressSonSingle: '/XxjAddressSon/GetXxjAddressSonSingle',
  /** 修改二级地址 */
  UpdateXxjAddressSon: '/XxjAddressSon/UpdateXxjAddressSon',
}
export const apiAutoXxjArea = {
  /** 新增地区 */
  AddXxjArea: '/XxjArea/AddXxjArea',
  /** 删除地区 */
  DeleteXxjArea: '/XxjArea/DeleteXxjArea',
  /** 获取地区列表 */
  GetXxjAreaList: '/XxjArea/GetXxjAreaList',
  /** 获取地区单条 */
  GetXxjAreaSingle: '/XxjArea/GetXxjAreaSingle',
  /** 修改地区 */
  UpdateXxjArea: '/XxjArea/UpdateXxjArea',
}
export const apiAutoXxjCity = {
  /** 新增城市 */
  AddXxjCity: '/XxjCity/AddXxjCity',
  /** 删除城市 */
  DeleteXxjCity: '/XxjCity/DeleteXxjCity',
  /** 获取城市列表 */
  GetXxjCityList: '/XxjCity/GetXxjCityList',
  /** 获取城市单条 */
  GetXxjCitySingle: '/XxjCity/GetXxjCitySingle',
  /** 修改城市 */
  UpdateXxjCity: '/XxjCity/UpdateXxjCity',
}
export const apiAutoXxjDataGroup = {
  /** 新增数据组 */
  AddXxjDataGroup: '/XxjDataGroup/AddXxjDataGroup',
  /** 新增材料关联 */
  AddXxjGroupMater: '/XxjDataGroup/AddXxjGroupMater',
  /** 复制组 */
  CopyXxjDataGroup: '/XxjDataGroup/CopyXxjDataGroup',
  /** 删除数据组 */
  DeleteXxjDataGroup: '/XxjDataGroup/DeleteXxjDataGroup',
  /** 删除材料关联 */
  DeleteXxjGroupMater: '/XxjDataGroup/DeleteXxjGroupMater',
  /** 获取概算总汇--导出 */
  ExportGroupMater: '/XxjDataGroup/ExportGroupMater',
  /** 获取材差表数据 */
  GetCcProjectList: '/XxjDataGroup/GetCcProjectList',
  /** 获取材料汇总数据 */
  GetGroupMaterWithYearPriceList: '/XxjDataGroup/GetGroupMaterWithYearPriceList',
  /** 获取数据组列表 */
  GetXxjDataGroupList: '/XxjDataGroup/GetXxjDataGroupList',
  /** 获取数据组---单条 */
  GetXxjDataGroupSingle: '/XxjDataGroup/GetXxjDataGroupSingle',
  /** 获取材料关联列表 */
  GetXxjGroupMaterList: '/XxjDataGroup/GetXxjGroupMaterList',
  /** 修改数据组 */
  UpdateXxjDataGroup: '/XxjDataGroup/UpdateXxjDataGroup',
  /** 批量修改数据组--排序 */
  UpdateXxjDataGroupSort: '/XxjDataGroup/UpdateXxjDataGroupSort',
  /** 批量修改材料排序 */
  UpdateXxjGroupMaterSort: '/XxjDataGroup/UpdateXxjGroupMaterSort',
}
export const apiAutoXxjOgPrice = {
  /** 新增原始价格 */
  AddXxjOgPrice: '/XxjOgPrice/AddXxjOgPrice',
  /** 删除原始价格 */
  DeleteXxjOgPrice: '/XxjOgPrice/DeleteXxjOgPrice',
  /** 获取原始价格列表 */
  GetXxjOgPriceList: '/XxjOgPrice/GetXxjOgPriceList',
  /** 修改原始价格 */
  UpdateXxjOgPrice: '/XxjOgPrice/UpdateXxjOgPrice',
}
export const apiAutoXxjOgTime = {
  /** 获取原始时间列表 */
  GetXxjOgTimeList: '/XxjOgTime/GetXxjOgTimeList',
}
export const apiAutoXxjOgType = {
  /** 获取原始类型列表 */
  GetXxjOgTypeList: '/XxjOgType/GetXxjOgTypeList',
}
export const apiAutoXxjZkCategory = {
  /** 获取类型列表--分组 */
  GetXxjZkCategoryList: '/XxjZkCategory/GetXxjZkCategoryList',
  /** 获取类型列表 */
  GetXxjZkCategoryListDb: '/XxjZkCategory/GetXxjZkCategoryListDb',
}
export const apiAutoXxjZkInfoYear = {
  /** 获取时间列表 */
  GetXxjZkInfoYearList: '/XxjZkInfoYear/GetXxjZkInfoYearList',
}
export const apiAutoXxjZkPrice = {
  /** 新增价格 */
  AddXxjZkPrice: '/XxjZkPrice/AddXxjZkPrice',
  /** 删除价格 */
  DeleteXxjZkPrice: '/XxjZkPrice/DeleteXxjZkPrice',
  /** 获取价格列表 */
  GetXxjZkPriceList: '/XxjZkPrice/GetXxjZkPriceList',
  /** 修改价格 */
  UpdateXxjZkPrice: '/XxjZkPrice/UpdateXxjZkPrice',
}
export const apiAutoXxjZkPriceV2 = {
  /** 创建城市地区 */
  CreateAreaConfig: '/XxjZkPriceV2/CreateAreaConfig',
  /** 创建城市配置 */
  CreateCityConfig: '/XxjZkPriceV2/CreateCityConfig',
  /** 删除城市地区 */
  DeleteAreaConfig: '/XxjZkPriceV2/DeleteAreaConfig',
  /** 删除城市配置 */
  DeleteCityConfig: '/XxjZkPriceV2/DeleteCityConfig',
  /** 获取城市地区列表 */
  GetAreaConfigList: '/XxjZkPriceV2/GetAreaConfigList',
  /** 获取城市配置列表 */
  GetCityConfigList: '/XxjZkPriceV2/GetCityConfigList',
  /** 获取城市地区列表 */
  GetUploadSqliteList: '/XxjZkPriceV2/GetUploadSqliteList',
  /** 所有类目 */
  GetXxjZkCategoryDisList: '/XxjZkPriceV2/GetXxjZkCategoryDisList',
  /** 类型信息列表 */
  GetXxjZkCategoryList: '/XxjZkPriceV2/GetXxjZkCategoryList',
  /** 所有年份期数 */
  GetXxjZkInfoYearList: '/XxjZkPriceV2/GetXxjZkInfoYearList',
  /** 获取价格列表 */
  GetXxjZkPriceList: '/XxjZkPriceV2/GetXxjZkPriceList',
  /** 获取物料列表 */
  GetXxjZkPriceMaterialList: '/XxjZkPriceV2/GetXxjZkPriceMaterialList',
  /** 更新城市地区 */
  UpdateAreaConfig: '/XxjZkPriceV2/UpdateAreaConfig',
  /** 修改城市配置 */
  UpdateCityConfig: '/XxjZkPriceV2/UpdateCityConfig',
}
export const apiAutoYdsc = {
  /**  */
  UploadResult: '/Ydsc/UploadResult',
}
export const apiAutoYongzhongApi = {
  /** 永中文件上传 */
  UpfileHttp: '/YongzhongApi/UpfileHttp',
}
export const apiAutoTest = {
  /**  */
  Index: '/api/Test',
}
export const apiAutoWPS = {
  /**  */
  GenarateWPSUrl: '/api/wps/genarate',
  /**  */
  GetHistory: '/v1/3rd/file/history',
  /**  */
  FileInfo: '/v1/3rd/file/info',
  /**  */
  NewFile: '/v1/3rd/file/new',
  /**  */
  Online: '/v1/3rd/file/online',
  /**  */
  SaveFile: '/v1/3rd/file/save',
  /**  */
  Version: '/v1/3rd/file/version',
  /**  */
  WPSNotify: '/v1/3rd/onnotify',
  /**  */
  GetUserInfo: '/v1/3rd/user/info',
}
